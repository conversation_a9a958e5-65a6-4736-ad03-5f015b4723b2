#include "plugin_interface.h"
#include "ui_manager.h"
#include "data_fetcher.h"
#include "stock_jumper.h"
#include "utils.h"
#include <commctrl.h>

const wchar_t* PluginManager::WINDOW_CLASS_NAME = L"TdxKplPluginWindow";

PluginManager& PluginManager::GetInstance() {
    static PluginManager instance;
    return instance;
}

bool PluginManager::Initialize() {
    LOG_INFO("Initializing PluginManager");
    
    if (m_bInitialized) {
        return true;
    }
    
    try {
        // 注册窗口类
        RegisterWindowClass();
        
        m_bInitialized = true;
        LOG_INFO("PluginManager initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize PluginManager: " + std::string(e.what()));
        return false;
    }
}

void PluginManager::Cleanup() {
    LOG_INFO("Cleaning up PluginManager");
    
    if (m_hMainWnd) {
        DestroyWindow(m_hMainWnd);
        m_hMainWnd = nullptr;
    }
    
    m_bInitialized = false;
}

HWND PluginManager::CreateMainWindow(HWND parent, int x, int y, int width, int height) {
    if (!m_bInitialized) {
        LOG_ERROR("PluginManager not initialized");
        return nullptr;
    }
    
    // 如果窗口已存在，直接返回
    if (m_hMainWnd && IsWindow(m_hMainWnd)) {
        return m_hMainWnd;
    }
    
    DWORD dwStyle = WS_OVERLAPPEDWINDOW;
    DWORD dwExStyle = WS_EX_APPWINDOW;
    
    if (parent) {
        dwStyle = WS_CHILD | WS_VISIBLE | WS_CLIPSIBLINGS | WS_CLIPCHILDREN;
        dwExStyle = 0;
    }
    
    m_hMainWnd = CreateWindowEx(
        dwExStyle,
        WINDOW_CLASS_NAME,
        L"开盘啦热榜",
        dwStyle,
        x, y, width, height,
        parent,
        nullptr,
        GetModuleHandle(nullptr),
        this
    );
    
    if (!m_hMainWnd) {
        LOG_ERROR("Failed to create main window: " + Utils::GetLastErrorString());
        return nullptr;
    }
    
    LOG_INFO("Main window created successfully");
    return m_hMainWnd;
}

void PluginManager::ShowMainWindow(int nCmdShow) {
    if (m_hMainWnd) {
        ShowWindow(m_hMainWnd, nCmdShow);
        UpdateWindow(m_hMainWnd);
    }
}

LRESULT PluginManager::HandleMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    return WindowProc(hwnd, msg, wParam, lParam);
}

void PluginManager::RegisterWindowClass() {
    WNDCLASSEX wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = WindowProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = sizeof(PluginManager*);
    wcex.hInstance = GetModuleHandle(nullptr);
    wcex.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = nullptr;
    wcex.lpszClassName = WINDOW_CLASS_NAME;
    wcex.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);
    
    if (!RegisterClassEx(&wcex)) {
        throw std::runtime_error("Failed to register window class");
    }
}

LRESULT CALLBACK PluginManager::WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    PluginManager* pThis = nullptr;
    
    if (msg == WM_NCCREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pThis = reinterpret_cast<PluginManager*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pThis));
    } else {
        pThis = reinterpret_cast<PluginManager*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    if (pThis) {
        switch (msg) {
        case WM_CREATE:
            pThis->CreateControls(hwnd);
            return 0;
            
        case WM_COMMAND:
            pThis->OnCommand(hwnd, wParam, lParam);
            return 0;
            
        case WM_NOTIFY:
            pThis->OnNotify(hwnd, wParam, lParam);
            return 0;
            
        case WM_SIZE:
            pThis->OnSize(hwnd, LOWORD(lParam), HIWORD(lParam));
            return 0;
            
        case WM_PAINT:
            pThis->OnPaint(hwnd);
            return 0;
            
        case WM_DESTROY:
            if (hwnd == pThis->m_hMainWnd) {
                pThis->m_hMainWnd = nullptr;
            }
            return 0;
        }
    }
    
    return DefWindowProc(hwnd, msg, wParam, lParam);
}

void PluginManager::CreateControls(HWND hwnd) {
    RECT rect;
    GetClientRect(hwnd, &rect);
    
    // 创建UI管理器并初始化控件
    UIManager* uiManager = new UIManager();
    if (uiManager->Initialize(hwnd)) {
        uiManager->CreateControls(hwnd, rect.right - rect.left, rect.bottom - rect.top);
        
        // 保存UI管理器指针
        SetWindowLongPtr(hwnd, GWLP_USERDATA + sizeof(PluginManager*), reinterpret_cast<LONG_PTR>(uiManager));
        
        // 获取控件句柄
        m_hListView = uiManager->GetListView();
        m_hComboBox = uiManager->GetComboBox();
        m_hRefreshBtn = uiManager->GetRefreshButton();
        m_hStatusText = uiManager->GetStatusText();
    }
}

void PluginManager::OnCommand(HWND hwnd, WPARAM wParam, LPARAM lParam) {
    UIManager* uiManager = reinterpret_cast<UIManager*>(
        GetWindowLongPtr(hwnd, GWLP_USERDATA + sizeof(PluginManager*)));
    
    if (uiManager) {
        uiManager->OnCommand(wParam, lParam);
    }
}

void PluginManager::OnNotify(HWND hwnd, WPARAM wParam, LPARAM lParam) {
    UIManager* uiManager = reinterpret_cast<UIManager*>(
        GetWindowLongPtr(hwnd, GWLP_USERDATA + sizeof(PluginManager*)));
    
    if (uiManager) {
        uiManager->OnNotify(wParam, lParam);
    }
}

void PluginManager::OnSize(HWND hwnd, int width, int height) {
    UIManager* uiManager = reinterpret_cast<UIManager*>(
        GetWindowLongPtr(hwnd, GWLP_USERDATA + sizeof(PluginManager*)));
    
    if (uiManager) {
        uiManager->ResizeControls(width, height);
    }
}

void PluginManager::OnPaint(HWND hwnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hwnd, &ps);
    
    // 简单的绘制背景
    RECT rect;
    GetClientRect(hwnd, &rect);
    FillRect(hdc, &rect, (HBRUSH)(COLOR_WINDOW + 1));
    
    EndPaint(hwnd, &ps);
}
