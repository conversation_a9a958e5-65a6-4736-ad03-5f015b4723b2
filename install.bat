@echo off
setlocal enabledelayedexpansion

echo 通达信开盘啦联动插件安装脚本
echo ================================

REM 检查DLL文件是否存在
if not exist "build\bin\TdxKplPlugin.dll" (
    echo 错误: 找不到 TdxKplPlugin.dll 文件
    echo 请先运行 build.bat 编译项目
    pause
    exit /b 1
)

REM 查找通达信安装目录
set TDX_PATH=
set TDX_DLL_PATH=

REM 常见安装路径
set PATHS[0]=C:\tdx
set PATHS[1]=C:\Program Files\tdx
set PATHS[2]=C:\Program Files (x86)\tdx
set PATHS[3]=D:\tdx
set PATHS[4]=E:\tdx

echo 正在查找通达信安装目录...

for /L %%i in (0,1,4) do (
    if exist "!PATHS[%%i]!\TdxW.exe" (
        set TDX_PATH=!PATHS[%%i]!
        set TDX_DLL_PATH=!PATHS[%%i]!\T0002\dlls
        echo 找到通达信安装目录: !TDX_PATH!
        goto :found
    )
)

REM 如果没找到，让用户手动输入
echo 未找到通达信安装目录，请手动输入:
set /p TDX_PATH=请输入通达信安装目录 (例如: C:\tdx): 

if not exist "%TDX_PATH%\TdxW.exe" (
    echo 错误: 指定目录中找不到 TdxW.exe
    pause
    exit /b 1
)

set TDX_DLL_PATH=%TDX_PATH%\T0002\dlls

:found

REM 检查插件目录是否存在
if not exist "%TDX_DLL_PATH%" (
    echo 创建插件目录: %TDX_DLL_PATH%
    mkdir "%TDX_DLL_PATH%"
    if !ERRORLEVEL! neq 0 (
        echo 错误: 无法创建插件目录，请以管理员身份运行
        pause
        exit /b 1
    )
)

echo.
echo 安装目录: %TDX_DLL_PATH%
echo.

REM 复制DLL文件
echo 复制 TdxKplPlugin.dll...
copy "build\bin\TdxKplPlugin.dll" "%TDX_DLL_PATH%\"
if %ERRORLEVEL% neq 0 (
    echo 错误: 复制DLL文件失败，请检查权限
    pause
    exit /b 1
)

REM 复制配置文件
echo 复制 config.ini...
copy "config.ini" "%TDX_DLL_PATH%\"
if %ERRORLEVEL% neq 0 (
    echo 警告: 复制配置文件失败
)

REM 更新配置文件中的通达信路径
echo 更新配置文件...
set CONFIG_FILE=%TDX_DLL_PATH%\config.ini
set TEMP_FILE=%TDX_DLL_PATH%\config_temp.ini

if exist "%CONFIG_FILE%" (
    (
        for /f "usebackq delims=" %%a in ("%CONFIG_FILE%") do (
            set line=%%a
            if "!line:~0,8!"=="tdx_path" (
                echo tdx_path=%TDX_PATH%\TdxW.exe
            ) else (
                echo !line!
            )
        )
    ) > "%TEMP_FILE%"
    
    move "%TEMP_FILE%" "%CONFIG_FILE%"
)

echo.
echo ================================
echo 安装完成！
echo.
echo 安装位置: %TDX_DLL_PATH%
echo 配置文件: %TDX_DLL_PATH%\config.ini
echo.
echo 使用说明:
echo 1. 重启通达信金融终端
echo 2. 在菜单栏中找到"开盘啦热榜"选项
echo 3. 点击打开插件窗口
echo 4. 双击股票行可跳转到K线图
echo.
echo 如需卸载，请删除以下文件:
echo - %TDX_DLL_PATH%\TdxKplPlugin.dll
echo - %TDX_DLL_PATH%\config.ini
echo.

pause
