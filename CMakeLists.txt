cmake_minimum_required(VERSION 3.16)
project(TdxKplPlugin)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找依赖包
find_package(PkgConfig REQUIRED)

# 添加包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/third_party/json/include)
include_directories(${CMAKE_SOURCE_DIR}/third_party/curl/include)

# 添加源文件
set(SOURCES
    src/main.cpp
    src/plugin_interface.cpp
    src/data_fetcher.cpp
    src/ui_manager.cpp
    src/stock_jumper.cpp
    src/utils.cpp
)

# 添加头文件
set(HEADERS
    include/plugin_interface.h
    include/data_fetcher.h
    include/ui_manager.h
    include/stock_jumper.h
    include/utils.h
    include/common.h
)

# 创建DLL
add_library(TdxKplPlugin SHARED ${SOURCES} ${HEADERS})

# 链接库
if(WIN32)
    target_link_libraries(TdxKplPlugin 
        ${CMAKE_SOURCE_DIR}/third_party/curl/lib/libcurl.lib
        wininet
        ws2_32
        user32
        gdi32
        comctl32
    )
    
    # 设置DLL导出
    set_target_properties(TdxKplPlugin PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS TRUE
        PREFIX ""
        SUFFIX ".dll"
    )
endif()

# 编译选项
if(MSVC)
    target_compile_options(TdxKplPlugin PRIVATE /W3)
else()
    target_compile_options(TdxKplPlugin PRIVATE -Wall -Wextra)
endif()

# 定义宏
target_compile_definitions(TdxKplPlugin PRIVATE
    UNICODE
    _UNICODE
    WIN32_LEAN_AND_MEAN
)

# 创建测试程序
option(BUILD_TESTS "Build test programs" ON)

if(BUILD_TESTS)
    # 测试程序源文件
    set(TEST_SOURCES
        test/test_main.cpp
        src/data_fetcher.cpp
        src/stock_jumper.cpp
        src/utils.cpp
    )

    # 创建测试可执行文件
    add_executable(TdxKplPluginTest ${TEST_SOURCES})

    # 链接库
    if(WIN32)
        target_link_libraries(TdxKplPluginTest
            wininet
            ws2_32
            user32
            gdi32
            comctl32
            shlwapi
        )
    endif()

    # 编译选项
    if(MSVC)
        target_compile_options(TdxKplPluginTest PRIVATE /W3)
    else()
        target_compile_options(TdxKplPluginTest PRIVATE -Wall -Wextra)
    endif()

    # 定义宏
    target_compile_definitions(TdxKplPluginTest PRIVATE
        UNICODE
        _UNICODE
        WIN32_LEAN_AND_MEAN
    )

    # 包含目录
    target_include_directories(TdxKplPluginTest PRIVATE
        ${CMAKE_SOURCE_DIR}/include
    )
endif()
