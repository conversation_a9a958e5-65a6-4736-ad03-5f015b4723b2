#pragma once

#include "common.h"

// 通达信插件标准接口
extern "C" {
    // 插件初始化
    __declspec(dllexport) BOOL WINAPI PluginInit();
    
    // 插件清理
    __declspec(dllexport) void WINAPI PluginCleanup();
    
    // 获取插件信息
    __declspec(dllexport) BOOL WINAPI GetPluginInfo(PluginInfo* info);
    
    // 创建插件窗口
    __declspec(dllexport) HWND WINAPI CreatePluginWindow(HWND parent, int x, int y, int width, int height);
    
    // 显示插件窗口
    __declspec(dllexport) BOOL WINAPI ShowPluginWindow(HWND hwnd, int nCmdShow);
    
    // 插件窗口消息处理
    __declspec(dllexport) LRESULT WINAPI PluginWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    // 获取插件菜单项
    __declspec(dllexport) int WINAPI GetMenuItems(char* menuItems, int maxSize);
    
    // 菜单项点击处理
    __declspec(dllexport) BOOL WINAPI OnMenuItemClick(int menuId);
}

// 插件管理类
class PluginManager {
public:
    static PluginManager& GetInstance();
    
    bool Initialize();
    void Cleanup();
    
    HWND CreateMainWindow(HWND parent, int x, int y, int width, int height);
    void ShowMainWindow(int nCmdShow);
    
    // 窗口消息处理
    LRESULT HandleMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
private:
    PluginManager() = default;
    ~PluginManager() = default;
    PluginManager(const PluginManager&) = delete;
    PluginManager& operator=(const PluginManager&) = delete;
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    void RegisterWindowClass();
    void CreateControls(HWND hwnd);
    void OnCommand(HWND hwnd, WPARAM wParam, LPARAM lParam);
    void OnNotify(HWND hwnd, WPARAM wParam, LPARAM lParam);
    void OnSize(HWND hwnd, int width, int height);
    void OnPaint(HWND hwnd);
    
private:
    HWND m_hMainWnd = nullptr;
    HWND m_hListView = nullptr;
    HWND m_hComboBox = nullptr;
    HWND m_hRefreshBtn = nullptr;
    HWND m_hStatusText = nullptr;
    
    bool m_bInitialized = false;
    static const wchar_t* WINDOW_CLASS_NAME;
};
