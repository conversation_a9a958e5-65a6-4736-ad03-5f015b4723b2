#include "plugin_interface.h"
#include "utils.h"
#include <windows.h>
#include <comdef.h>
#include <commctrl.h>

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // 初始化日志
        Logger::GetInstance().Initialize("TdxKplPlugin.log");
        LOG_INFO("Plugin DLL loaded");
        break;
        
    case DLL_THREAD_ATTACH:
        break;
        
    case DLL_THREAD_DETACH:
        break;
        
    case DLL_PROCESS_DETACH:
        LOG_INFO("Plugin DLL unloaded");
        Logger::GetInstance().Cleanup();
        break;
    }
    return TRUE;
}

// 插件初始化
BOOL WINAPI PluginInit() {
    LOG_INFO("PluginInit called");
    
    try {
        // 初始化COM
        CoInitialize(nullptr);
        
        // 初始化通用控件
        INITCOMMONCONTROLSEX icex;
        icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
        icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_BAR_CLASSES;
        InitCommonControlsEx(&icex);
        
        // 初始化插件管理器
        if (!PluginManager::GetInstance().Initialize()) {
            LOG_ERROR("Failed to initialize PluginManager");
            return FALSE;
        }
        
        LOG_INFO("Plugin initialized successfully");
        return TRUE;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in PluginInit: " + std::string(e.what()));
        return FALSE;
    }
    catch (...) {
        LOG_ERROR("Unknown exception in PluginInit");
        return FALSE;
    }
}

// 插件清理
void WINAPI PluginCleanup() {
    LOG_INFO("PluginCleanup called");
    
    try {
        PluginManager::GetInstance().Cleanup();
        CoUninitialize();
        LOG_INFO("Plugin cleanup completed");
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in PluginCleanup: " + std::string(e.what()));
    }
    catch (...) {
        LOG_ERROR("Unknown exception in PluginCleanup");
    }
}

// 获取插件信息
BOOL WINAPI GetPluginInfo(PluginInfo* info) {
    if (!info) {
        return FALSE;
    }
    
    try {
        strcpy_s(info->name, "开盘啦热榜插件");
        strcpy_s(info->version, "1.0.0");
        strcpy_s(info->author, "TdxKpl Plugin Team");
        strcpy_s(info->description, "显示开盘啦热榜数据，支持跳转到通达信K线图");
        
        return TRUE;
    }
    catch (...) {
        return FALSE;
    }
}

// 创建插件窗口
HWND WINAPI CreatePluginWindow(HWND parent, int x, int y, int width, int height) {
    LOG_INFO("CreatePluginWindow called");
    
    try {
        return PluginManager::GetInstance().CreateMainWindow(parent, x, y, width, height);
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in CreatePluginWindow: " + std::string(e.what()));
        return nullptr;
    }
    catch (...) {
        LOG_ERROR("Unknown exception in CreatePluginWindow");
        return nullptr;
    }
}

// 显示插件窗口
BOOL WINAPI ShowPluginWindow(HWND hwnd, int nCmdShow) {
    LOG_INFO("ShowPluginWindow called");
    
    try {
        if (!hwnd) {
            return FALSE;
        }
        
        ShowWindow(hwnd, nCmdShow);
        UpdateWindow(hwnd);
        
        PluginManager::GetInstance().ShowMainWindow(nCmdShow);
        
        return TRUE;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in ShowPluginWindow: " + std::string(e.what()));
        return FALSE;
    }
    catch (...) {
        LOG_ERROR("Unknown exception in ShowPluginWindow");
        return FALSE;
    }
}

// 插件窗口消息处理
LRESULT WINAPI PluginWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    try {
        return PluginManager::GetInstance().HandleMessage(hwnd, msg, wParam, lParam);
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in PluginWindowProc: " + std::string(e.what()));
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    catch (...) {
        LOG_ERROR("Unknown exception in PluginWindowProc");
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

// 获取插件菜单项
int WINAPI GetMenuItems(char* menuItems, int maxSize) {
    if (!menuItems || maxSize <= 0) {
        return 0;
    }
    
    try {
        const char* menu = "开盘啦热榜|显示热榜窗口";
        int len = strlen(menu);
        
        if (len >= maxSize) {
            return 0;
        }
        
        strcpy_s(menuItems, maxSize, menu);
        return 1;  // 返回菜单项数量
    }
    catch (...) {
        return 0;
    }
}

// 菜单项点击处理
BOOL WINAPI OnMenuItemClick(int menuId) {
    LOG_INFO("OnMenuItemClick called with menuId: " + std::to_string(menuId));
    
    try {
        switch (menuId) {
        case 0:  // 显示热榜窗口
            {
                HWND hwnd = PluginManager::GetInstance().CreateMainWindow(nullptr, 100, 100, 800, 600);
                if (hwnd) {
                    ShowWindow(hwnd, SW_SHOW);
                    return TRUE;
                }
            }
            break;
        }
        
        return FALSE;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in OnMenuItemClick: " + std::string(e.what()));
        return FALSE;
    }
    catch (...) {
        LOG_ERROR("Unknown exception in OnMenuItemClick");
        return FALSE;
    }
}
