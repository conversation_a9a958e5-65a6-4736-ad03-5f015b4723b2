#pragma once

#include "common.h"
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

// 数据获取回调函数类型
using DataCallback = std::function<void(const std::vector<StockInfo>&, bool success, const std::string& error)>;

// 数据获取器类
class DataFetcher {
public:
    DataFetcher();
    ~DataFetcher();
    
    // 初始化
    bool Initialize();
    
    // 清理
    void Cleanup();
    
    // 异步获取热榜数据
    void FetchHotListAsync(HotListType type, DataCallback callback);
    
    // 同步获取热榜数据
    bool FetchHotList(HotListType type, std::vector<StockInfo>& stocks, std::string& error);
    
    // 停止所有请求
    void StopAllRequests();
    
    // 设置请求超时时间（秒）
    void SetTimeout(int seconds);
    
    // 设置代理
    void SetProxy(const std::string& proxy);
    
private:
    // HTTP请求函数
    bool HttpGet(const std::string& url, std::string& response, std::string& error);
    
    // 解析开盘啦API响应
    bool ParseKplResponse(const std::string& response, std::vector<StockInfo>& stocks);
    
    // 解析TuShare API响应
    bool ParseTushareResponse(const std::string& response, std::vector<StockInfo>& stocks);
    
    // 构建API URL
    std::string BuildApiUrl(HotListType type);
    
    // 获取当前日期字符串
    std::string GetCurrentDate();
    
    // 工作线程函数
    void WorkerThread();
    
private:
    struct RequestTask {
        HotListType type;
        DataCallback callback;
        std::chrono::steady_clock::time_point timestamp;
    };
    
    std::thread m_workerThread;
    std::atomic<bool> m_bRunning;
    std::mutex m_taskMutex;
    std::vector<RequestTask> m_tasks;
    
    int m_timeout = 10;  // 默认10秒超时
    std::string m_proxy;
    
    // API配置
    static const std::string TUSHARE_BASE_URL;
    static const std::string KPL_BASE_URL;
    std::string m_tushareToken;  // TuShare API Token
};

// HTTP工具类
class HttpClient {
public:
    HttpClient();
    ~HttpClient();
    
    bool Initialize();
    void Cleanup();
    
    bool Get(const std::string& url, std::string& response, std::string& error);
    bool Post(const std::string& url, const std::string& data, std::string& response, std::string& error);
    
    void SetTimeout(int seconds);
    void SetProxy(const std::string& proxy);
    void SetUserAgent(const std::string& userAgent);
    
private:
    void* m_curl = nullptr;  // CURL handle
    int m_timeout = 10;
    std::string m_proxy;
    std::string m_userAgent;
    
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* response);
};
