@echo off
echo 通达信开盘啦联动插件构建脚本
echo ================================

REM 检查是否存在构建目录
if not exist build (
    echo 创建构建目录...
    mkdir build
)

cd build

echo 生成项目文件...
cmake .. -G "Visual Studio 16 2019" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

echo 编译项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 构建完成！
echo DLL文件位置: build\bin\TdxKplPlugin.dll
echo.
echo 安装说明:
echo 1. 将 TdxKplPlugin.dll 复制到通达信安装目录下的 T0002\dlls 文件夹
echo 2. 将 config.ini 复制到 DLL 同目录
echo 3. 重启通达信，在菜单中找到"开盘啦热榜"选项
echo.

pause
