#pragma once

#include "common.h"

// 股票跳转管理器
class StockJumper {
public:
    StockJumper();
    ~StockJumper();
    
    // 初始化
    bool Initialize();
    
    // 清理
    void Cleanup();
    
    // 跳转到通达信K线图
    bool JumpToKLine(const StockInfo& stock);
    
    // 跳转到通达信分时图
    bool JumpToTimeShare(const StockInfo& stock);
    
    // 跳转到通达信F10资料
    bool JumpToF10(const StockInfo& stock);
    
    // 检查通达信是否运行
    bool IsTdxRunning();
    
    // 启动通达信
    bool StartTdx();
    
    // 设置通达信安装路径
    void SetTdxPath(const std::string& path);
    
    // 获取通达信安装路径
    std::string GetTdxPath();
    
private:
    // 查找通达信窗口
    HWND FindTdxWindow();
    
    // 发送股票代码到通达信
    bool SendStockCodeToTdx(const std::string& code, const std::string& market);
    
    // 通过DDE发送命令
    bool SendDDECommand(const std::string& command);
    
    // 通过剪贴板发送股票代码
    bool SendViaClipboard(const std::string& code);
    
    // 通过模拟按键发送
    bool SendViaKeyboard(const std::string& code);
    
    // 格式化股票代码
    std::string FormatStockCode(const StockInfo& stock);
    
    // 从注册表获取通达信路径
    std::string GetTdxPathFromRegistry();
    
    // 等待窗口激活
    bool WaitForWindowActive(HWND hwnd, int timeoutMs = 3000);
    
private:
    std::string m_tdxPath;
    HWND m_hTdxWnd = nullptr;
    
    // 通达信窗口类名和标题
    static const std::string TDX_WINDOW_CLASS;
    static const std::string TDX_WINDOW_TITLE;
    
    // DDE相关
    DWORD m_ddeInstance = 0;
    HSZ m_hszService = nullptr;
    HSZ m_hszTopic = nullptr;
    HCONV m_hConv = nullptr;
    
    // 跳转方式枚举
    enum class JumpMethod {
        DDE,        // DDE通信
        CLIPBOARD,  // 剪贴板
        KEYBOARD    // 模拟按键
    };
    
    JumpMethod m_jumpMethod = JumpMethod::KEYBOARD;
};

// DDE回调函数
HDDEDATA CALLBACK DdeCallback(UINT uType, UINT uFmt, HCONV hconv, HSZ hsz1, HSZ hsz2, 
                             HDDEDATA hdata, ULONG_PTR dwData1, ULONG_PTR dwData2);

// 窗口枚举回调
BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam);

// 通达信命令定义
namespace TdxCommands {
    const std::string SHOW_KLINE = "0";      // 显示K线图
    const std::string SHOW_TIMESHARE = "1";  // 显示分时图
    const std::string SHOW_F10 = "F10";      // 显示F10资料
    const std::string SHOW_LEVEL2 = "2";     // 显示Level2行情
}
