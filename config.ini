# 通达信开盘啦联动插件配置文件

# 通达信安装路径
tdx_path=C:\tdx\TdxW.exe

# TuShare API Token (可选，用于获取更准确的数据)
tushare_token=

# HTTP请求配置
http_timeout=10
http_proxy=

# 日志配置
log_level=INFO
log_max_size=10485760

# 数据刷新间隔（秒）
refresh_interval=30

# 默认显示的榜单类型
# 0=涨停榜, 1=跌停榜, 2=成交量榜, 3=成交额榜, 4=涨跌幅榜, 5=热门概念
default_list_type=0

# 窗口配置
window_width=800
window_height=600
window_x=100
window_y=100

# 股票跳转方式
# 0=DDE, 1=剪贴板, 2=键盘模拟
jump_method=2

# 是否自动启动通达信
auto_start_tdx=true

# 数据源配置
# primary=tushare, secondary=mock
data_source=mock
