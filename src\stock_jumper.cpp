#include "stock_jumper.h"
#include "utils.h"
#include <ddeml.h>
#include <tlhelp32.h>
#include <sstream>

#pragma comment(lib, "user32.lib")

// 静态常量定义
const std::string StockJumper::TDX_WINDOW_CLASS = "TdxW_MainFrame_Class";
const std::string StockJumper::TDX_WINDOW_TITLE = "通达信金融终端";

StockJumper::StockJumper() : m_ddeInstance(0) {
}

StockJumper::~StockJumper() {
    Cleanup();
}

bool StockJumper::Initialize() {
    LOG_INFO("Initializing StockJumper");
    
    try {
        // 获取通达信路径
        m_tdxPath = GetTdxPathFromRegistry();
        if (m_tdxPath.empty()) {
            m_tdxPath = GetTdxPath();
        }
        
        // 初始化DDE
        if (DdeInitialize(&m_ddeInstance, DdeCallback, APPCLASS_STANDARD, 0) != DMLERR_NO_ERROR) {
            LOG_WARNING("Failed to initialize DDE, will use alternative methods");
            m_jumpMethod = JumpMethod::KEYBOARD;
        } else {
            m_jumpMethod = JumpMethod::DDE;
            
            // 创建DDE字符串
            m_hszService = DdeCreateStringHandle(m_ddeInstance, L"TdxService", CP_WINUNICODE);
            m_hszTopic = DdeCreateStringHandle(m_ddeInstance, L"TdxTopic", CP_WINUNICODE);
        }
        
        LOG_INFO("StockJumper initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize StockJumper: " + std::string(e.what()));
        return false;
    }
}

void StockJumper::Cleanup() {
    LOG_INFO("Cleaning up StockJumper");
    
    if (m_hConv) {
        DdeDisconnect(m_hConv);
        m_hConv = nullptr;
    }
    
    if (m_hszService) {
        DdeFreeStringHandle(m_ddeInstance, m_hszService);
        m_hszService = nullptr;
    }
    
    if (m_hszTopic) {
        DdeFreeStringHandle(m_ddeInstance, m_hszTopic);
        m_hszTopic = nullptr;
    }
    
    if (m_ddeInstance) {
        DdeUninitialize(m_ddeInstance);
        m_ddeInstance = 0;
    }
}

bool StockJumper::JumpToKLine(const StockInfo& stock) {
    LOG_INFO("Jumping to K-line for stock: " + stock.code);
    
    if (!IsTdxRunning()) {
        LOG_WARNING("TDX not running, trying to start it");
        if (!StartTdx()) {
            LOG_ERROR("Failed to start TDX");
            return false;
        }
        
        // 等待通达信启动
        Sleep(3000);
    }
    
    // 查找通达信窗口
    m_hTdxWnd = FindTdxWindow();
    if (!m_hTdxWnd) {
        LOG_ERROR("Cannot find TDX window");
        return false;
    }
    
    // 激活通达信窗口
    SetForegroundWindow(m_hTdxWnd);
    if (!WaitForWindowActive(m_hTdxWnd)) {
        LOG_WARNING("TDX window not activated properly");
    }
    
    // 发送股票代码
    std::string formattedCode = FormatStockCode(stock);
    
    switch (m_jumpMethod) {
    case JumpMethod::DDE:
        return SendDDECommand("VIEW " + formattedCode + " KLINE");
        
    case JumpMethod::CLIPBOARD:
        return SendViaClipboard(formattedCode);
        
    case JumpMethod::KEYBOARD:
    default:
        return SendViaKeyboard(formattedCode);
    }
}

bool StockJumper::JumpToTimeShare(const StockInfo& stock) {
    LOG_INFO("Jumping to time-share for stock: " + stock.code);
    
    if (!IsTdxRunning()) {
        if (!StartTdx()) {
            return false;
        }
        Sleep(3000);
    }
    
    m_hTdxWnd = FindTdxWindow();
    if (!m_hTdxWnd) {
        return false;
    }
    
    SetForegroundWindow(m_hTdxWnd);
    std::string formattedCode = FormatStockCode(stock);
    
    switch (m_jumpMethod) {
    case JumpMethod::DDE:
        return SendDDECommand("VIEW " + formattedCode + " TIMESHARE");
        
    case JumpMethod::KEYBOARD:
    default:
        // 先发送股票代码，然后按Enter进入分时图
        if (SendViaKeyboard(formattedCode)) {
            Sleep(100);
            keybd_event(VK_RETURN, 0, 0, 0);
            keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);
            return true;
        }
        return false;
    }
}

bool StockJumper::JumpToF10(const StockInfo& stock) {
    LOG_INFO("Jumping to F10 for stock: " + stock.code);
    
    if (!IsTdxRunning()) {
        if (!StartTdx()) {
            return false;
        }
        Sleep(3000);
    }
    
    m_hTdxWnd = FindTdxWindow();
    if (!m_hTdxWnd) {
        return false;
    }
    
    SetForegroundWindow(m_hTdxWnd);
    std::string formattedCode = FormatStockCode(stock);
    
    // 先跳转到股票，然后按F10
    if (SendViaKeyboard(formattedCode)) {
        Sleep(100);
        keybd_event(VK_F10, 0, 0, 0);
        keybd_event(VK_F10, 0, KEYEVENTF_KEYUP, 0);
        return true;
    }
    
    return false;
}

bool StockJumper::IsTdxRunning() {
    return FindTdxWindow() != nullptr;
}

bool StockJumper::StartTdx() {
    if (m_tdxPath.empty()) {
        LOG_ERROR("TDX path not set");
        return false;
    }
    
    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);
    
    if (CreateProcessA(
        m_tdxPath.c_str(),
        nullptr,
        nullptr,
        nullptr,
        FALSE,
        0,
        nullptr,
        nullptr,
        &si,
        &pi
    )) {
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        LOG_INFO("TDX started successfully");
        return true;
    } else {
        LOG_ERROR("Failed to start TDX: " + Utils::GetLastErrorString());
        return false;
    }
}

void StockJumper::SetTdxPath(const std::string& path) {
    m_tdxPath = path;
}

std::string StockJumper::GetTdxPath() {
    if (!m_tdxPath.empty()) {
        return m_tdxPath;
    }
    
    // 从配置文件读取
    ConfigManager& config = ConfigManager::GetInstance();
    std::string path = config.GetString("tdx_path", "");
    
    if (!path.empty() && Utils::FileExists(path)) {
        return path;
    }
    
    // 从注册表读取
    return GetTdxPathFromRegistry();
}

HWND StockJumper::FindTdxWindow() {
    // 首先尝试通过窗口类名查找
    HWND hwnd = FindWindowA(TDX_WINDOW_CLASS.c_str(), nullptr);
    if (hwnd) {
        return hwnd;
    }
    
    // 如果找不到，尝试通过窗口标题查找
    hwnd = FindWindowA(nullptr, TDX_WINDOW_TITLE.c_str());
    if (hwnd) {
        return hwnd;
    }
    
    // 枚举所有窗口查找
    struct FindWindowData {
        HWND result;
        const std::string* className;
        const std::string* title;
    };
    
    FindWindowData data = { nullptr, &TDX_WINDOW_CLASS, &TDX_WINDOW_TITLE };
    
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        FindWindowData* pData = reinterpret_cast<FindWindowData*>(lParam);
        
        char className[256];
        char windowTitle[256];
        
        GetClassNameA(hwnd, className, sizeof(className));
        GetWindowTextA(hwnd, windowTitle, sizeof(windowTitle));
        
        if (strstr(className, pData->className->c_str()) || 
            strstr(windowTitle, pData->title->c_str())) {
            pData->result = hwnd;
            return FALSE;  // 停止枚举
        }
        
        return TRUE;  // 继续枚举
    }, reinterpret_cast<LPARAM>(&data));
    
    return data.result;
}

bool StockJumper::SendStockCodeToTdx(const std::string& code, const std::string& market) {
    return SendViaKeyboard(code);
}

bool StockJumper::SendDDECommand(const std::string& command) {
    if (!m_ddeInstance || !m_hszService || !m_hszTopic) {
        return false;
    }
    
    // 连接到DDE服务
    if (!m_hConv) {
        m_hConv = DdeConnect(m_ddeInstance, m_hszService, m_hszTopic, nullptr);
        if (!m_hConv) {
            LOG_WARNING("Failed to connect to DDE service");
            return false;
        }
    }
    
    // 发送命令
    std::wstring wcommand = Utils::StringToWString(command);
    HSZ hszCommand = DdeCreateStringHandle(m_ddeInstance, wcommand.c_str(), CP_WINUNICODE);
    
    HDDEDATA result = DdeClientTransaction(
        nullptr, 0, m_hConv, hszCommand, CF_TEXT, XTYP_EXECUTE, 5000, nullptr
    );
    
    DdeFreeStringHandle(m_ddeInstance, hszCommand);
    
    if (result) {
        DdeFreeDataHandle(result);
        return true;
    }
    
    return false;
}

bool StockJumper::SendViaClipboard(const std::string& code) {
    // 将股票代码复制到剪贴板
    if (!OpenClipboard(m_hTdxWnd)) {
        return false;
    }

    EmptyClipboard();

    std::wstring wcode = Utils::StringToWString(code);
    size_t size = (wcode.length() + 1) * sizeof(wchar_t);

    HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, size);
    if (hMem) {
        wchar_t* pMem = static_cast<wchar_t*>(GlobalLock(hMem));
        wcscpy_s(pMem, wcode.length() + 1, wcode.c_str());
        GlobalUnlock(hMem);

        SetClipboardData(CF_UNICODETEXT, hMem);
    }

    CloseClipboard();

    // 发送Ctrl+V粘贴
    keybd_event(VK_CONTROL, 0, 0, 0);
    keybd_event('V', 0, 0, 0);
    keybd_event('V', 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);

    Sleep(100);

    // 按Enter确认
    keybd_event(VK_RETURN, 0, 0, 0);
    keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);

    return true;
}

bool StockJumper::SendViaKeyboard(const std::string& code) {
    // 模拟键盘输入股票代码
    for (char c : code) {
        if (c >= '0' && c <= '9') {
            BYTE vk = c - '0' + VK_NUMPAD0;  // 使用数字键盘
            keybd_event(vk, 0, 0, 0);
            keybd_event(vk, 0, KEYEVENTF_KEYUP, 0);
            Sleep(50);  // 短暂延迟
        }
    }

    // 按Enter确认
    Sleep(100);
    keybd_event(VK_RETURN, 0, 0, 0);
    keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);

    return true;
}

std::string StockJumper::FormatStockCode(const StockInfo& stock) {
    // 通达信通常只需要6位数字代码
    std::string code = stock.code;

    // 移除非数字字符
    std::string result;
    for (char c : code) {
        if (c >= '0' && c <= '9') {
            result += c;
        }
    }

    // 确保是6位数字
    if (result.length() == 6) {
        return result;
    } else if (result.length() < 6) {
        // 补零
        return std::string(6 - result.length(), '0') + result;
    } else {
        // 截取前6位
        return result.substr(0, 6);
    }
}

std::string StockJumper::GetTdxPathFromRegistry() {
    // 常见的通达信注册表路径
    std::vector<std::string> regPaths = {
        "SOFTWARE\\tdx\\tdxw",
        "SOFTWARE\\WOW6432Node\\tdx\\tdxw",
        "SOFTWARE\\通达信\\通达信金融终端",
        "SOFTWARE\\WOW6432Node\\通达信\\通达信金融终端"
    };

    for (const auto& path : regPaths) {
        std::string exePath = Utils::ReadRegistryString(HKEY_LOCAL_MACHINE, path, "Path");
        if (!exePath.empty()) {
            exePath += "\\TdxW.exe";
            if (Utils::FileExists(exePath)) {
                return exePath;
            }
        }
    }

    // 尝试默认安装路径
    std::vector<std::string> defaultPaths = {
        "C:\\tdx\\TdxW.exe",
        "C:\\Program Files\\tdx\\TdxW.exe",
        "C:\\Program Files (x86)\\tdx\\TdxW.exe",
        "D:\\tdx\\TdxW.exe"
    };

    for (const auto& path : defaultPaths) {
        if (Utils::FileExists(path)) {
            return path;
        }
    }

    return "";
}

bool StockJumper::WaitForWindowActive(HWND hwnd, int timeoutMs) {
    int elapsed = 0;
    const int checkInterval = 100;

    while (elapsed < timeoutMs) {
        if (GetForegroundWindow() == hwnd) {
            return true;
        }

        Sleep(checkInterval);
        elapsed += checkInterval;
    }

    return false;
}

// DDE回调函数
HDDEDATA CALLBACK DdeCallback(UINT uType, UINT uFmt, HCONV hconv, HSZ hsz1, HSZ hsz2,
                             HDDEDATA hdata, ULONG_PTR dwData1, ULONG_PTR dwData2) {
    switch (uType) {
    case XTYP_DISCONNECT:
        return (HDDEDATA)TRUE;

    case XTYP_ERROR:
        return (HDDEDATA)TRUE;

    default:
        return (HDDEDATA)NULL;
    }
}

// 窗口枚举回调
BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    // 这个函数在FindTdxWindow中的lambda中实现
    return TRUE;
}
