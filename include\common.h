#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <map>

// 通达信插件接口相关定义
#define PLUGIN_INFO_SIZE 256

// 股票数据结构
struct StockInfo {
    std::string code;        // 股票代码
    std::string name;        // 股票名称
    std::string market;      // 市场（SH/SZ）
    double price;            // 当前价格
    double change_rate;      // 涨跌幅
    double change_amount;    // 涨跌额
    int64_t volume;          // 成交量
    double turnover;         // 成交额
    int rank;                // 排名
    std::string reason;      // 上榜原因
};

// 热榜类型枚举
enum class HotListType {
    LIMIT_UP,       // 涨停榜
    LIMIT_DOWN,     // 跌停榜
    VOLUME,         // 成交量榜
    TURNOVER,       // 成交额榜
    CHANGE_RATE,    // 涨跌幅榜
    HOT_CONCEPT     // 热门概念
};

// 通达信插件信息结构
struct PluginInfo {
    char name[PLUGIN_INFO_SIZE];
    char version[PLUGIN_INFO_SIZE];
    char author[PLUGIN_INFO_SIZE];
    char description[PLUGIN_INFO_SIZE];
};

// 窗口消息定义
#define WM_STOCK_CLICK (WM_USER + 1001)
#define WM_REFRESH_DATA (WM_USER + 1002)
#define WM_CHANGE_LIST_TYPE (WM_USER + 1003)

// 控件ID定义
#define ID_LISTVIEW_STOCKS 2001
#define ID_COMBO_LIST_TYPE 2002
#define ID_BUTTON_REFRESH 2003
#define ID_STATIC_STATUS 2004

// 工具函数宏
#define SAFE_DELETE(p) if(p) { delete p; p = nullptr; }
#define SAFE_DELETE_ARRAY(p) if(p) { delete[] p; p = nullptr; }

// 字符串转换函数
std::wstring StringToWString(const std::string& str);
std::string WStringToString(const std::wstring& wstr);

// 日志级别
enum class LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR
};

// 简单日志函数声明
void WriteLog(LogLevel level, const std::string& message);
