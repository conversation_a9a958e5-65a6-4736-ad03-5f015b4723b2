#include "utils.h"
#include <windows.h>
#include <shlwapi.h>
#include <wininet.h>
#include <codecvt>
#include <locale>
#include <fstream>
#include <ctime>

#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "wininet.lib")

// Utils类实现
std::wstring Utils::StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

std::string Utils::WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::string Utils::UTF8ToGBK(const std::string& utf8Str) {
    // UTF8 -> Unicode -> GBK
    std::wstring unicode = StringToWString(utf8Str);
    
    int size_needed = WideCharToMultiByte(CP_ACP, 0, &unicode[0], (int)unicode.size(), NULL, 0, NULL, NULL);
    std::string gbkStr(size_needed, 0);
    WideCharToMultiByte(CP_ACP, 0, &unicode[0], (int)unicode.size(), &gbkStr[0], size_needed, NULL, NULL);
    return gbkStr;
}

std::string Utils::GBKToUTF8(const std::string& gbkStr) {
    // GBK -> Unicode -> UTF8
    int size_needed = MultiByteToWideChar(CP_ACP, 0, &gbkStr[0], (int)gbkStr.size(), NULL, 0);
    std::wstring unicode(size_needed, 0);
    MultiByteToWideChar(CP_ACP, 0, &gbkStr[0], (int)gbkStr.size(), &unicode[0], size_needed);
    
    return WStringToString(unicode);
}

std::string Utils::FormatDouble(double value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::string Utils::FormatPercent(double value, int precision) {
    return FormatDouble(value, precision) + "%";
}

std::string Utils::FormatVolume(int64_t volume) {
    if (volume >= 100000000) {  // 亿
        return FormatDouble(volume / 100000000.0, 2) + "亿";
    } else if (volume >= 10000) {  // 万
        return FormatDouble(volume / 10000.0, 2) + "万";
    } else {
        return std::to_string(volume);
    }
}

std::string Utils::FormatMoney(double money) {
    if (money >= 100000000) {  // 亿
        return FormatDouble(money / 100000000.0, 2) + "亿";
    } else if (money >= 10000) {  // 万
        return FormatDouble(money / 10000.0, 2) + "万";
    } else {
        return FormatDouble(money, 2);
    }
}

std::string Utils::GetCurrentDateTime() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::tm tm;
    localtime_s(&tm, &time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

std::string Utils::GetCurrentDate() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::tm tm;
    localtime_s(&tm, &time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d");
    return oss.str();
}

std::string Utils::GetCurrentTime() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::tm tm;
    localtime_s(&tm, &time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%H:%M:%S");
    return oss.str();
}

std::string Utils::FormatTimestamp(int64_t timestamp) {
    time_t time = static_cast<time_t>(timestamp);
    std::tm tm;
    localtime_s(&tm, &time);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

bool Utils::FileExists(const std::string& path) {
    DWORD dwAttrib = GetFileAttributesA(path.c_str());
    return (dwAttrib != INVALID_FILE_ATTRIBUTES && !(dwAttrib & FILE_ATTRIBUTE_DIRECTORY));
}

bool Utils::DirectoryExists(const std::string& path) {
    DWORD dwAttrib = GetFileAttributesA(path.c_str());
    return (dwAttrib != INVALID_FILE_ATTRIBUTES && (dwAttrib & FILE_ATTRIBUTE_DIRECTORY));
}

bool Utils::CreateDirectory(const std::string& path) {
    return CreateDirectoryA(path.c_str(), NULL) || GetLastError() == ERROR_ALREADY_EXISTS;
}

std::string Utils::GetExecutablePath() {
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    return std::string(path);
}

std::string Utils::GetExecutableDirectory() {
    std::string path = GetExecutablePath();
    size_t pos = path.find_last_of("\\/");
    if (pos != std::string::npos) {
        return path.substr(0, pos);
    }
    return "";
}

std::string Utils::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey;
    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return "";
    }
    
    DWORD dataSize = 0;
    if (RegQueryValueExA(hSubKey, valueName.c_str(), NULL, NULL, NULL, &dataSize) != ERROR_SUCCESS) {
        RegCloseKey(hSubKey);
        return "";
    }
    
    std::string result(dataSize, 0);
    if (RegQueryValueExA(hSubKey, valueName.c_str(), NULL, NULL, 
                        reinterpret_cast<LPBYTE>(&result[0]), &dataSize) == ERROR_SUCCESS) {
        // 移除末尾的null字符
        if (!result.empty() && result.back() == '\0') {
            result.pop_back();
        }
    } else {
        result.clear();
    }
    
    RegCloseKey(hSubKey);
    return result;
}

bool Utils::WriteRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName, const std::string& value) {
    HKEY hSubKey;
    if (RegCreateKeyExA(hKey, subKey.c_str(), 0, NULL, REG_OPTION_NON_VOLATILE, 
                       KEY_WRITE, NULL, &hSubKey, NULL) != ERROR_SUCCESS) {
        return false;
    }
    
    LONG result = RegSetValueExA(hSubKey, valueName.c_str(), 0, REG_SZ, 
                                reinterpret_cast<const BYTE*>(value.c_str()), 
                                static_cast<DWORD>(value.length() + 1));
    
    RegCloseKey(hSubKey);
    return result == ERROR_SUCCESS;
}

bool Utils::IsInternetConnected() {
    return InternetCheckConnectionA("http://www.baidu.com", FLAG_ICC_FORCE_CONNECTION, 0);
}

std::string Utils::UrlEncode(const std::string& str) {
    std::ostringstream encoded;
    encoded.fill('0');
    encoded << std::hex;
    
    for (char c : str) {
        if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            encoded << c;
        } else {
            encoded << std::uppercase;
            encoded << '%' << std::setw(2) << int(static_cast<unsigned char>(c));
            encoded << std::nouppercase;
        }
    }
    
    return encoded.str();
}

std::string Utils::UrlDecode(const std::string& str) {
    std::string decoded;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            int value;
            std::istringstream is(str.substr(i + 1, 2));
            if (is >> std::hex >> value) {
                decoded += static_cast<char>(value);
                i += 2;
            } else {
                decoded += str[i];
            }
        } else if (str[i] == '+') {
            decoded += ' ';
        } else {
            decoded += str[i];
        }
    }
    return decoded;
}

std::string Utils::JsonEscape(const std::string& str) {
    std::string escaped;
    for (char c : str) {
        switch (c) {
        case '"': escaped += "\\\""; break;
        case '\\': escaped += "\\\\"; break;
        case '\b': escaped += "\\b"; break;
        case '\f': escaped += "\\f"; break;
        case '\n': escaped += "\\n"; break;
        case '\r': escaped += "\\r"; break;
        case '\t': escaped += "\\t"; break;
        default: escaped += c; break;
        }
    }
    return escaped;
}

std::string Utils::JsonUnescape(const std::string& str) {
    std::string unescaped;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '\\' && i + 1 < str.length()) {
            switch (str[i + 1]) {
            case '"': unescaped += '"'; ++i; break;
            case '\\': unescaped += '\\'; ++i; break;
            case 'b': unescaped += '\b'; ++i; break;
            case 'f': unescaped += '\f'; ++i; break;
            case 'n': unescaped += '\n'; ++i; break;
            case 'r': unescaped += '\r'; ++i; break;
            case 't': unescaped += '\t'; ++i; break;
            default: unescaped += str[i]; break;
            }
        } else {
            unescaped += str[i];
        }
    }
    return unescaped;
}

std::string Utils::GetMarketFromCode(const std::string& code) {
    if (code.empty()) return "";

    // 根据股票代码判断市场
    if (code.length() >= 6) {
        std::string prefix = code.substr(0, 2);
        if (prefix == "60" || prefix == "68" || prefix == "90") {
            return "SH";  // 上海
        } else if (prefix == "00" || prefix == "30" || prefix == "20") {
            return "SZ";  // 深圳
        }
    }

    return "SH";  // 默认上海
}

bool Utils::IsValidStockCode(const std::string& code) {
    if (code.length() != 6) return false;

    for (char c : code) {
        if (c < '0' || c > '9') return false;
    }

    return true;
}

std::string Utils::FormatStockCode(const std::string& code, const std::string& market) {
    if (market == "SH") {
        return "SH" + code;
    } else if (market == "SZ") {
        return "SZ" + code;
    } else {
        return code;
    }
}

bool Utils::LoadConfig(const std::string& configFile, std::map<std::string, std::string>& config) {
    std::ifstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }

        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除首尾空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            config[key] = value;
        }
    }

    return true;
}

bool Utils::SaveConfig(const std::string& configFile, const std::map<std::string, std::string>& config) {
    std::ofstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    for (const auto& pair : config) {
        file << pair.first << "=" << pair.second << std::endl;
    }

    return true;
}

std::string Utils::GetLastErrorString() {
    return GetLastErrorString(GetLastError());
}

std::string Utils::GetLastErrorString(DWORD errorCode) {
    if (errorCode == 0) return "";

    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, NULL
    );

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}

// Logger实现
Logger& Logger::GetInstance() {
    static Logger instance;
    return instance;
}

void Logger::Initialize(const std::string& logFile) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_logFile = logFile;
    m_logStream.open(logFile, std::ios::app);

    if (m_logStream.is_open()) {
        WriteLog(LogLevel::INFO, "Logger initialized");
    }
}

void Logger::Cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_logStream.is_open()) {
        WriteLog(LogLevel::INFO, "Logger cleanup");
        m_logStream.close();
    }
}

void Logger::WriteLog(LogLevel level, const std::string& message) {
    WriteLog(level, "", message);
}

void Logger::WriteLog(LogLevel level, const std::string& function, const std::string& message) {
    if (level < m_logLevel) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_logStream.is_open()) {
        return;
    }

    // 检查文件大小
    m_logStream.seekp(0, std::ios::end);
    if (m_logStream.tellp() > static_cast<std::streampos>(m_maxFileSize)) {
        RotateLogFile();
    }

    // 写入日志
    std::string timestamp = Utils::GetCurrentDateTime();
    std::string levelStr = GetLogLevelString(level);

    m_logStream << "[" << timestamp << "] [" << levelStr << "]";
    if (!function.empty()) {
        m_logStream << " [" << function << "]";
    }
    m_logStream << " " << message << std::endl;
    m_logStream.flush();
}

void Logger::SetLogLevel(LogLevel level) {
    m_logLevel = level;
}

void Logger::SetMaxFileSize(size_t maxSize) {
    m_maxFileSize = maxSize;
}

void Logger::RotateLogFile() {
    if (m_logStream.is_open()) {
        m_logStream.close();
    }

    // 重命名当前日志文件
    std::string backupFile = m_logFile + ".bak";
    MoveFileA(m_logFile.c_str(), backupFile.c_str());

    // 重新打开日志文件
    m_logStream.open(m_logFile, std::ios::app);
}

std::string Logger::GetLogLevelString(LogLevel level) {
    switch (level) {
    case LogLevel::DEBUG: return "DEBUG";
    case LogLevel::INFO: return "INFO";
    case LogLevel::WARNING: return "WARNING";
    case LogLevel::ERROR: return "ERROR";
    default: return "UNKNOWN";
    }
}

// ConfigManager实现
ConfigManager& ConfigManager::GetInstance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::Initialize(const std::string& configFile) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_configFile = configFile;
    return Utils::LoadConfig(configFile, m_config);
}

void ConfigManager::Cleanup() {
    Save();
}

std::string ConfigManager::GetString(const std::string& key, const std::string& defaultValue) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_config.find(key);
    if (it != m_config.end()) {
        return it->second;
    }
    return defaultValue;
}

int ConfigManager::GetInt(const std::string& key, int defaultValue) {
    std::string value = GetString(key, "");
    if (value.empty()) {
        return defaultValue;
    }

    try {
        return std::stoi(value);
    } catch (...) {
        return defaultValue;
    }
}

double ConfigManager::GetDouble(const std::string& key, double defaultValue) {
    std::string value = GetString(key, "");
    if (value.empty()) {
        return defaultValue;
    }

    try {
        return std::stod(value);
    } catch (...) {
        return defaultValue;
    }
}

bool ConfigManager::GetBool(const std::string& key, bool defaultValue) {
    std::string value = GetString(key, "");
    if (value.empty()) {
        return defaultValue;
    }

    return value == "true" || value == "1" || value == "yes";
}

void ConfigManager::SetString(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config[key] = value;
}

void ConfigManager::SetInt(const std::string& key, int value) {
    SetString(key, std::to_string(value));
}

void ConfigManager::SetDouble(const std::string& key, double value) {
    SetString(key, std::to_string(value));
}

void ConfigManager::SetBool(const std::string& key, bool value) {
    SetString(key, value ? "true" : "false");
}

bool ConfigManager::Save() {
    std::lock_guard<std::mutex> lock(m_mutex);
    return Utils::SaveConfig(m_configFile, m_config);
}

// 全局日志函数实现
void WriteLog(LogLevel level, const std::string& message) {
    Logger::GetInstance().WriteLog(level, message);
}
