#pragma once

#include "common.h"
#include <sstream>
#include <iomanip>
#include <fstream>
#include <chrono>

// 工具函数类
class Utils {
public:
    // 字符串转换
    static std::wstring StringToWString(const std::string& str);
    static std::string WStringToString(const std::wstring& wstr);
    static std::string UTF8ToGBK(const std::string& utf8Str);
    static std::string GBKToUTF8(const std::string& gbkStr);
    
    // 数字格式化
    static std::string FormatDouble(double value, int precision = 2);
    static std::string FormatPercent(double value, int precision = 2);
    static std::string FormatVolume(int64_t volume);
    static std::string FormatMoney(double money);
    
    // 时间相关
    static std::string GetCurrentDateTime();
    static std::string GetCurrentDate();
    static std::string GetCurrentTime();
    static std::string FormatTimestamp(int64_t timestamp);
    
    // 文件操作
    static bool FileExists(const std::string& path);
    static bool DirectoryExists(const std::string& path);
    static bool CreateDirectory(const std::string& path);
    static std::string GetExecutablePath();
    static std::string GetExecutableDirectory();
    
    // 注册表操作
    static std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    static bool WriteRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName, const std::string& value);
    
    // 网络相关
    static bool IsInternetConnected();
    static std::string UrlEncode(const std::string& str);
    static std::string UrlDecode(const std::string& str);
    
    // JSON相关（简单实现）
    static std::string JsonEscape(const std::string& str);
    static std::string JsonUnescape(const std::string& str);
    
    // 股票代码相关
    static std::string GetMarketFromCode(const std::string& code);
    static bool IsValidStockCode(const std::string& code);
    static std::string FormatStockCode(const std::string& code, const std::string& market);
    
    // 配置文件操作
    static bool LoadConfig(const std::string& configFile, std::map<std::string, std::string>& config);
    static bool SaveConfig(const std::string& configFile, const std::map<std::string, std::string>& config);
    
    // 错误处理
    static std::string GetLastErrorString();
    static std::string GetLastErrorString(DWORD errorCode);
    
private:
    Utils() = delete;
};

// 日志管理器
class Logger {
public:
    static Logger& GetInstance();
    
    void Initialize(const std::string& logFile);
    void Cleanup();
    
    void WriteLog(LogLevel level, const std::string& message);
    void WriteLog(LogLevel level, const std::string& function, const std::string& message);
    
    void SetLogLevel(LogLevel level);
    void SetMaxFileSize(size_t maxSize);
    
private:
    Logger() = default;
    ~Logger() = default;
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    void RotateLogFile();
    std::string GetLogLevelString(LogLevel level);
    
private:
    std::string m_logFile;
    std::ofstream m_logStream;
    LogLevel m_logLevel = LogLevel::INFO;
    size_t m_maxFileSize = 10 * 1024 * 1024;  // 10MB
    std::mutex m_mutex;
};

// 配置管理器
class ConfigManager {
public:
    static ConfigManager& GetInstance();
    
    bool Initialize(const std::string& configFile);
    void Cleanup();
    
    std::string GetString(const std::string& key, const std::string& defaultValue = "");
    int GetInt(const std::string& key, int defaultValue = 0);
    double GetDouble(const std::string& key, double defaultValue = 0.0);
    bool GetBool(const std::string& key, bool defaultValue = false);
    
    void SetString(const std::string& key, const std::string& value);
    void SetInt(const std::string& key, int value);
    void SetDouble(const std::string& key, double value);
    void SetBool(const std::string& key, bool value);
    
    bool Save();
    
private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
private:
    std::string m_configFile;
    std::map<std::string, std::string> m_config;
    std::mutex m_mutex;
};

// 宏定义
#define LOG_DEBUG(msg) Logger::GetInstance().WriteLog(LogLevel::DEBUG, __FUNCTION__, msg)
#define LOG_INFO(msg) Logger::GetInstance().WriteLog(LogLevel::INFO, __FUNCTION__, msg)
#define LOG_WARNING(msg) Logger::GetInstance().WriteLog(LogLevel::WARNING, __FUNCTION__, msg)
#define LOG_ERROR(msg) Logger::GetInstance().WriteLog(LogLevel::ERROR, __FUNCTION__, msg)
