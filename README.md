# 通达信开盘啦联动插件

这是一个用于通达信的C++插件，可以显示开盘啦热榜数据，并支持点击股票后自动跳转到通达信的K线图页面。

## 功能特性

- 📊 显示开盘啦热榜数据（涨停榜、跌停榜、成交量榜等）
- 🔄 支持实时数据刷新
- 🖱️ 双击股票自动跳转到通达信K线图
- 🎨 美观的界面设计，支持涨跌颜色显示
- ⚙️ 灵活的配置选项
- 📝 完整的日志记录

## 系统要求

- Windows 7/8/10/11
- 通达信金融终端
- Visual Studio 2019 或更高版本（用于编译）
- CMake 3.16 或更高版本

## 编译说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd TdxKplPlugin
```

### 2. 创建构建目录
```bash
mkdir build
cd build
```

### 3. 生成项目文件
```bash
cmake ..
```

### 4. 编译项目
```bash
cmake --build . --config Release
```

编译完成后，会在 `build/bin` 目录下生成 `TdxKplPlugin.dll` 文件。

## 安装使用

### 1. 复制DLL文件
将编译生成的 `TdxKplPlugin.dll` 文件复制到通达信安装目录下的 `T0002\dlls` 文件夹中。

### 2. 配置插件
编辑 `config.ini` 文件，设置通达信路径和其他配置项：
```ini
tdx_path=C:\tdx\TdxW.exe
tushare_token=your_token_here
```

### 3. 启动通达信
重新启动通达信，在菜单栏中找到"开盘啦热榜"选项。

## 配置说明

### config.ini 配置文件

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| tdx_path | 通达信可执行文件路径 | C:\tdx\TdxW.exe |
| tushare_token | TuShare API Token（可选） | 空 |
| http_timeout | HTTP请求超时时间（秒） | 10 |
| refresh_interval | 数据刷新间隔（秒） | 30 |
| default_list_type | 默认榜单类型 | 0（涨停榜） |
| jump_method | 股票跳转方式 | 2（键盘模拟） |

### 榜单类型

- 0: 涨停榜
- 1: 跌停榜  
- 2: 成交量榜
- 3: 成交额榜
- 4: 涨跌幅榜
- 5: 热门概念

### 跳转方式

- 0: DDE通信（推荐，但可能不兼容所有版本）
- 1: 剪贴板方式
- 2: 键盘模拟（兼容性最好）

## 使用说明

1. **启动插件**：在通达信菜单中选择"开盘啦热榜"
2. **选择榜单**：使用下拉框选择要查看的榜单类型
3. **刷新数据**：点击"刷新"按钮获取最新数据
4. **跳转K线**：双击任意股票行，自动跳转到该股票的K线图

## 数据源

插件支持多种数据源：

1. **TuShare API**：需要注册并获取Token，数据更准确
2. **模拟数据**：用于测试和演示

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查DLL文件是否放在正确位置
   - 确认通达信版本兼容性
   - 查看日志文件获取详细错误信息

2. **无法跳转到K线图**
   - 确认通达信正在运行
   - 尝试不同的跳转方式
   - 检查股票代码格式是否正确

3. **数据无法刷新**
   - 检查网络连接
   - 验证API Token是否有效
   - 查看HTTP请求日志

### 日志文件

插件会生成详细的日志文件 `TdxKplPlugin.log`，包含：
- 插件启动和关闭信息
- 数据获取过程
- 错误和警告信息
- 用户操作记录

## 开发说明

### 项目结构
```
TdxKplPlugin/
├── include/          # 头文件
├── src/             # 源文件
├── CMakeLists.txt   # CMake配置
├── config.ini       # 配置文件
└── README.md        # 说明文档
```

### 主要模块

- **PluginInterface**: 通达信插件接口实现
- **DataFetcher**: 数据获取模块
- **UIManager**: 界面管理模块
- **StockJumper**: 股票跳转功能
- **Utils**: 工具函数库

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意**：本插件仅供学习和研究使用，投资有风险，请谨慎决策。
