#pragma once

// 版本信息
#define PLUGIN_VERSION_MAJOR 1
#define PLUGIN_VERSION_MINOR 0
#define PLUGIN_VERSION_PATCH 0
#define PLUGIN_VERSION_BUILD 1

// 版本字符串
#define PLUGIN_VERSION_STRING "*******"
#define PLUGIN_VERSION_STRING_SHORT "1.0.0"

// 产品信息
#define PLUGIN_PRODUCT_NAME "通达信开盘啦联动插件"
#define PLUGIN_PRODUCT_NAME_EN "TDX KaiPanLa Integration Plugin"
#define PLUGIN_COMPANY_NAME "TdxKpl Plugin Team"
#define PLUGIN_COPYRIGHT "Copyright (C) 2024 TdxKpl Plugin Team"

// 文件信息
#define PLUGIN_FILE_DESCRIPTION "通达信开盘啦热榜数据显示插件"
#define PLUGIN_FILE_DESCRIPTION_EN "TDX KaiPanLa Hot List Data Display Plugin"
#define PLUGIN_INTERNAL_NAME "TdxKplPlugin"
#define PLUGIN_ORIGINAL_FILENAME "TdxKplPlugin.dll"

// 构建信息
#define PLUGIN_BUILD_DATE __DATE__
#define PLUGIN_BUILD_TIME __TIME__

// API版本
#define PLUGIN_API_VERSION 1

// 最小通达信版本要求
#define MIN_TDX_VERSION "8.0"

// 功能特性标志
#define FEATURE_HOT_LIST        1
#define FEATURE_STOCK_JUMP      1
#define FEATURE_REAL_TIME_DATA  1
#define FEATURE_CUSTOM_UI       1
#define FEATURE_CONFIG_FILE     1
#define FEATURE_LOGGING         1
