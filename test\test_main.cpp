#include "../include/data_fetcher.h"
#include "../include/stock_jumper.h"
#include "../include/utils.h"
#include <iostream>
#include <windows.h>

void TestDataFetcher() {
    std::cout << "测试数据获取模块..." << std::endl;
    
    DataFetcher fetcher;
    if (!fetcher.Initialize()) {
        std::cout << "数据获取模块初始化失败" << std::endl;
        return;
    }
    
    std::vector<StockInfo> stocks;
    std::string error;
    
    if (fetcher.FetchHotList(HotListType::LIMIT_UP, stocks, error)) {
        std::cout << "成功获取 " << stocks.size() << " 只股票数据" << std::endl;
        
        for (size_t i = 0; i < std::min(stocks.size(), size_t(5)); ++i) {
            const auto& stock = stocks[i];
            std::cout << "股票 " << (i+1) << ": " 
                      << stock.code << " " << stock.name 
                      << " 价格:" << stock.price 
                      << " 涨跌幅:" << stock.change_rate << "%" 
                      << std::endl;
        }
    } else {
        std::cout << "获取数据失败: " << error << std::endl;
    }
    
    fetcher.Cleanup();
}

void TestStockJumper() {
    std::cout << "\n测试股票跳转模块..." << std::endl;
    
    StockJumper jumper;
    if (!jumper.Initialize()) {
        std::cout << "股票跳转模块初始化失败" << std::endl;
        return;
    }
    
    // 检查通达信是否运行
    if (jumper.IsTdxRunning()) {
        std::cout << "通达信正在运行" << std::endl;
        
        // 测试跳转功能
        StockInfo testStock;
        testStock.code = "000001";
        testStock.name = "平安银行";
        testStock.market = "SZ";
        
        std::cout << "尝试跳转到 " << testStock.name << " K线图..." << std::endl;
        if (jumper.JumpToKLine(testStock)) {
            std::cout << "跳转成功" << std::endl;
        } else {
            std::cout << "跳转失败" << std::endl;
        }
    } else {
        std::cout << "通达信未运行" << std::endl;
        
        // 尝试启动通达信
        std::cout << "尝试启动通达信..." << std::endl;
        if (jumper.StartTdx()) {
            std::cout << "通达信启动成功" << std::endl;
        } else {
            std::cout << "通达信启动失败" << std::endl;
        }
    }
    
    jumper.Cleanup();
}

void TestUtils() {
    std::cout << "\n测试工具函数..." << std::endl;
    
    // 测试字符串转换
    std::string testStr = "测试字符串";
    std::wstring wstr = Utils::StringToWString(testStr);
    std::string backStr = Utils::WStringToString(wstr);
    std::cout << "字符串转换测试: " << (testStr == backStr ? "通过" : "失败") << std::endl;
    
    // 测试数字格式化
    double testValue = 12345.678;
    std::string formatted = Utils::FormatDouble(testValue, 2);
    std::cout << "数字格式化测试: " << testValue << " -> " << formatted << std::endl;
    
    // 测试成交量格式化
    int64_t volume = 123456789;
    std::string volumeStr = Utils::FormatVolume(volume);
    std::cout << "成交量格式化测试: " << volume << " -> " << volumeStr << std::endl;
    
    // 测试时间格式化
    std::string currentTime = Utils::GetCurrentDateTime();
    std::cout << "当前时间: " << currentTime << std::endl;
    
    // 测试股票代码验证
    std::string validCode = "000001";
    std::string invalidCode = "abc123";
    std::cout << "股票代码验证测试: " 
              << validCode << " " << (Utils::IsValidStockCode(validCode) ? "有效" : "无效") << ", "
              << invalidCode << " " << (Utils::IsValidStockCode(invalidCode) ? "有效" : "无效") 
              << std::endl;
}

void TestLogger() {
    std::cout << "\n测试日志系统..." << std::endl;
    
    Logger& logger = Logger::GetInstance();
    logger.Initialize("test.log");
    
    LOG_INFO("这是一条信息日志");
    LOG_WARNING("这是一条警告日志");
    LOG_ERROR("这是一条错误日志");
    LOG_DEBUG("这是一条调试日志");
    
    std::cout << "日志已写入 test.log 文件" << std::endl;
    
    logger.Cleanup();
}

void TestConfig() {
    std::cout << "\n测试配置管理..." << std::endl;
    
    ConfigManager& config = ConfigManager::GetInstance();
    if (config.Initialize("test_config.ini")) {
        std::cout << "配置文件初始化成功" << std::endl;
        
        // 设置一些测试值
        config.SetString("test_string", "Hello World");
        config.SetInt("test_int", 42);
        config.SetDouble("test_double", 3.14159);
        config.SetBool("test_bool", true);
        
        // 保存配置
        if (config.Save()) {
            std::cout << "配置保存成功" << std::endl;
        }
        
        // 读取配置
        std::string str = config.GetString("test_string", "");
        int num = config.GetInt("test_int", 0);
        double dbl = config.GetDouble("test_double", 0.0);
        bool flag = config.GetBool("test_bool", false);
        
        std::cout << "读取配置: " 
                  << "string=" << str 
                  << ", int=" << num 
                  << ", double=" << dbl 
                  << ", bool=" << (flag ? "true" : "false") 
                  << std::endl;
    } else {
        std::cout << "配置文件初始化失败" << std::endl;
    }
}

int main() {
    std::cout << "通达信开盘啦联动插件测试程序" << std::endl;
    std::cout << "================================" << std::endl;
    
    // 初始化COM
    CoInitialize(nullptr);
    
    try {
        TestUtils();
        TestLogger();
        TestConfig();
        TestDataFetcher();
        TestStockJumper();
        
        std::cout << "\n所有测试完成！" << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
    }
    
    // 清理COM
    CoUninitialize();
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}
