#include "ui_manager.h"
#include "data_fetcher.h"
#include "stock_jumper.h"
#include "utils.h"
#include <windowsx.h>
#include <sstream>
#include <iomanip>

// 静态常量定义
const COLORREF UIManager::COLOR_RED = RGB(255, 0, 0);
const COLORREF UIManager::COLOR_GREEN = RGB(0, 128, 0);
const COLORREF UIManager::COLOR_BLACK = RGB(0, 0, 0);

const int UIManager::COL_WIDTH_RANK = 50;
const int UIManager::COL_WIDTH_CODE = 80;
const int UIManager::COL_WIDTH_NAME = 120;
const int UIManager::COL_WIDTH_PRICE = 80;
const int UIManager::COL_WIDTH_CHANGE = 100;
const int UIManager::COL_WIDTH_VOLUME = 120;
const int UIManager::COL_WIDTH_REASON = 150;

UIManager::UIManager() {
}

UIManager::~UIManager() {
    Cleanup();
}

bool UIManager::Initialize(HWND parentWnd) {
    m_hParentWnd = parentWnd;
    
    // 创建字体
    m_hFont = CreateFont(
        14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"微软雅黑"
    );
    
    m_hBoldFont = CreateFont(
        14, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"微软雅黑"
    );
    
    return true;
}

void UIManager::Cleanup() {
    if (m_hFont) {
        DeleteObject(m_hFont);
        m_hFont = nullptr;
    }
    
    if (m_hBoldFont) {
        DeleteObject(m_hBoldFont);
        m_hBoldFont = nullptr;
    }
}

bool UIManager::CreateControls(HWND parentWnd, int width, int height) {
    const int MARGIN = 10;
    const int CONTROL_HEIGHT = 25;
    const int BUTTON_WIDTH = 80;
    const int COMBO_WIDTH = 150;
    
    // 创建榜单类型选择框
    m_hComboBox = CreateWindow(
        WC_COMBOBOX, L"",
        WS_CHILD | WS_VISIBLE | WS_VSCROLL | CBS_DROPDOWNLIST,
        MARGIN, MARGIN, COMBO_WIDTH, 200,
        parentWnd, (HMENU)ID_COMBO_LIST_TYPE, GetModuleHandle(nullptr), nullptr
    );
    
    // 创建刷新按钮
    m_hRefreshBtn = CreateWindow(
        L"BUTTON", L"刷新",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        MARGIN + COMBO_WIDTH + MARGIN, MARGIN, BUTTON_WIDTH, CONTROL_HEIGHT,
        parentWnd, (HMENU)ID_BUTTON_REFRESH, GetModuleHandle(nullptr), nullptr
    );
    
    // 创建状态文本
    m_hStatusText = CreateWindow(
        L"STATIC", L"就绪",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        MARGIN + COMBO_WIDTH + MARGIN + BUTTON_WIDTH + MARGIN, MARGIN + 5, 
        width - (MARGIN + COMBO_WIDTH + MARGIN + BUTTON_WIDTH + MARGIN * 2), CONTROL_HEIGHT,
        parentWnd, (HMENU)ID_STATIC_STATUS, GetModuleHandle(nullptr), nullptr
    );
    
    // 创建股票列表
    m_hListView = CreateWindow(
        WC_LISTVIEW, L"",
        WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SINGLESEL | WS_BORDER,
        MARGIN, MARGIN + CONTROL_HEIGHT + MARGIN, 
        width - MARGIN * 2, height - (MARGIN + CONTROL_HEIGHT + MARGIN * 2),
        parentWnd, (HMENU)ID_LISTVIEW_STOCKS, GetModuleHandle(nullptr), nullptr
    );
    
    if (!m_hComboBox || !m_hRefreshBtn || !m_hStatusText || !m_hListView) {
        LOG_ERROR("Failed to create controls");
        return false;
    }
    
    // 设置字体
    SendMessage(m_hComboBox, WM_SETFONT, (WPARAM)m_hFont, TRUE);
    SendMessage(m_hRefreshBtn, WM_SETFONT, (WPARAM)m_hFont, TRUE);
    SendMessage(m_hStatusText, WM_SETFONT, (WPARAM)m_hFont, TRUE);
    SendMessage(m_hListView, WM_SETFONT, (WPARAM)m_hFont, TRUE);
    
    // 初始化控件
    InitializeComboBox();
    InitializeListView();
    
    LOG_INFO("UI controls created successfully");
    return true;
}

void UIManager::InitializeComboBox() {
    // 添加榜单类型选项
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"涨停榜");
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"跌停榜");
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"成交量榜");
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"成交额榜");
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"涨跌幅榜");
    SendMessage(m_hComboBox, CB_ADDSTRING, 0, (LPARAM)L"热门概念");
    
    // 设置默认选择
    SendMessage(m_hComboBox, CB_SETCURSEL, 0, 0);
}

void UIManager::InitializeListView() {
    // 设置ListView扩展样式
    ListView_SetExtendedListViewStyle(m_hListView, 
        LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES | LVS_EX_DOUBLEBUFFER);
    
    // 添加列
    AddListViewColumn(0, L"排名", COL_WIDTH_RANK);
    AddListViewColumn(1, L"代码", COL_WIDTH_CODE);
    AddListViewColumn(2, L"名称", COL_WIDTH_NAME);
    AddListViewColumn(3, L"现价", COL_WIDTH_PRICE);
    AddListViewColumn(4, L"涨跌幅", COL_WIDTH_CHANGE);
    AddListViewColumn(5, L"成交量", COL_WIDTH_VOLUME);
    AddListViewColumn(6, L"上榜原因", COL_WIDTH_REASON);
}

void UIManager::AddListViewColumn(int index, const std::wstring& text, int width) {
    LVCOLUMN lvc = {};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;
    lvc.pszText = const_cast<LPWSTR>(text.c_str());
    lvc.cx = width;
    lvc.iSubItem = index;
    
    ListView_InsertColumn(m_hListView, index, &lvc);
}

void UIManager::UpdateStockList(const std::vector<StockInfo>& stocks) {
    m_stocks = stocks;
    
    // 清空现有项
    ListView_DeleteAllItems(m_hListView);
    
    // 添加新项
    for (size_t i = 0; i < stocks.size(); ++i) {
        AddListViewItem(static_cast<int>(i), stocks[i]);
    }
    
    // 更新状态
    SetStatusText("已加载 " + std::to_string(stocks.size()) + " 只股票");
    
    LOG_INFO("Updated stock list with " + std::to_string(stocks.size()) + " items");
}

void UIManager::AddListViewItem(int index, const StockInfo& stock) {
    LVITEM lvi = {};
    lvi.mask = LVIF_TEXT | LVIF_PARAM;
    lvi.iItem = index;
    lvi.iSubItem = 0;
    lvi.lParam = index;  // 存储索引用于后续操作
    
    // 排名
    std::wstring rankText = std::to_wstring(stock.rank);
    lvi.pszText = const_cast<LPWSTR>(rankText.c_str());
    int itemIndex = ListView_InsertItem(m_hListView, &lvi);
    
    // 代码
    std::wstring codeText = Utils::StringToWString(stock.code);
    ListView_SetItemText(m_hListView, itemIndex, 1, const_cast<LPWSTR>(codeText.c_str()));
    
    // 名称
    std::wstring nameText = Utils::StringToWString(stock.name);
    ListView_SetItemText(m_hListView, itemIndex, 2, const_cast<LPWSTR>(nameText.c_str()));
    
    // 现价
    std::wstring priceText = FormatNumber(stock.price);
    ListView_SetItemText(m_hListView, itemIndex, 3, const_cast<LPWSTR>(priceText.c_str()));
    
    // 涨跌幅
    std::wstring changeText = FormatNumber(stock.change_rate) + L"%";
    ListView_SetItemText(m_hListView, itemIndex, 4, const_cast<LPWSTR>(changeText.c_str()));
    
    // 成交量
    std::wstring volumeText = FormatVolume(stock.volume);
    ListView_SetItemText(m_hListView, itemIndex, 5, const_cast<LPWSTR>(volumeText.c_str()));
    
    // 上榜原因
    std::wstring reasonText = Utils::StringToWString(stock.reason);
    ListView_SetItemText(m_hListView, itemIndex, 6, const_cast<LPWSTR>(reasonText.c_str()));
}

void UIManager::ClearStockList() {
    ListView_DeleteAllItems(m_hListView);
    m_stocks.clear();
    SetStatusText("列表已清空");
}

void UIManager::SetStatusText(const std::string& text) {
    std::wstring wtext = Utils::StringToWString(text);
    SetWindowText(m_hStatusText, wtext.c_str());
}

StockInfo UIManager::GetSelectedStock() {
    int selectedIndex = ListView_GetNextItem(m_hListView, -1, LVNI_SELECTED);
    
    if (selectedIndex >= 0 && selectedIndex < static_cast<int>(m_stocks.size())) {
        return m_stocks[selectedIndex];
    }
    
    return StockInfo{};  // 返回空的股票信息
}

HotListType UIManager::GetSelectedListType() {
    int selection = static_cast<int>(SendMessage(m_hComboBox, CB_GETCURSEL, 0, 0));
    
    switch (selection) {
    case 0: return HotListType::LIMIT_UP;
    case 1: return HotListType::LIMIT_DOWN;
    case 2: return HotListType::VOLUME;
    case 3: return HotListType::TURNOVER;
    case 4: return HotListType::CHANGE_RATE;
    case 5: return HotListType::HOT_CONCEPT;
    default: return HotListType::LIMIT_UP;
    }
}

void UIManager::SetListType(HotListType type) {
    int index = static_cast<int>(type);
    SendMessage(m_hComboBox, CB_SETCURSEL, index, 0);
    m_currentListType = type;
}

void UIManager::ResizeControls(int width, int height) {
    const int MARGIN = 10;
    const int CONTROL_HEIGHT = 25;
    const int BUTTON_WIDTH = 80;
    const int COMBO_WIDTH = 150;
    
    if (m_hComboBox) {
        SetWindowPos(m_hComboBox, nullptr, MARGIN, MARGIN, COMBO_WIDTH, 200, SWP_NOZORDER);
    }
    
    if (m_hRefreshBtn) {
        SetWindowPos(m_hRefreshBtn, nullptr, 
                    MARGIN + COMBO_WIDTH + MARGIN, MARGIN, BUTTON_WIDTH, CONTROL_HEIGHT, SWP_NOZORDER);
    }
    
    if (m_hStatusText) {
        SetWindowPos(m_hStatusText, nullptr,
                    MARGIN + COMBO_WIDTH + MARGIN + BUTTON_WIDTH + MARGIN, MARGIN + 5,
                    width - (MARGIN + COMBO_WIDTH + MARGIN + BUTTON_WIDTH + MARGIN * 2), CONTROL_HEIGHT,
                    SWP_NOZORDER);
    }
    
    if (m_hListView) {
        SetWindowPos(m_hListView, nullptr,
                    MARGIN, MARGIN + CONTROL_HEIGHT + MARGIN,
                    width - MARGIN * 2, height - (MARGIN + CONTROL_HEIGHT + MARGIN * 2),
                    SWP_NOZORDER);
    }
}

void UIManager::OnCommand(WPARAM wParam, LPARAM lParam) {
    WORD commandId = LOWORD(wParam);
    WORD notifyCode = HIWORD(wParam);

    switch (commandId) {
    case ID_BUTTON_REFRESH:
        if (notifyCode == BN_CLICKED) {
            // 刷新数据
            SetStatusText("正在刷新数据...");

            // 异步获取数据
            DataFetcher fetcher;
            if (fetcher.Initialize()) {
                HotListType type = GetSelectedListType();
                fetcher.FetchHotListAsync(type, [this](const std::vector<StockInfo>& stocks, bool success, const std::string& error) {
                    if (success) {
                        UpdateStockList(stocks);
                    } else {
                        SetStatusText("刷新失败: " + error);
                        LOG_ERROR("Failed to refresh data: " + error);
                    }
                });
            }
        }
        break;

    case ID_COMBO_LIST_TYPE:
        if (notifyCode == CBN_SELCHANGE) {
            // 榜单类型改变，自动刷新
            m_currentListType = GetSelectedListType();
            PostMessage(m_hParentWnd, WM_COMMAND, MAKEWPARAM(ID_BUTTON_REFRESH, BN_CLICKED), (LPARAM)m_hRefreshBtn);
        }
        break;
    }
}

void UIManager::OnNotify(WPARAM wParam, LPARAM lParam) {
    LPNMHDR pnmh = (LPNMHDR)lParam;

    if (pnmh->hwndFrom == m_hListView) {
        switch (pnmh->code) {
        case NM_DBLCLK:
            {
                // 双击股票项，跳转到K线图
                StockInfo selectedStock = GetSelectedStock();
                if (!selectedStock.code.empty()) {
                    StockJumper jumper;
                    if (jumper.Initialize()) {
                        if (jumper.JumpToKLine(selectedStock)) {
                            SetStatusText("已跳转到 " + selectedStock.name + " K线图");
                            LOG_INFO("Jumped to K-line for stock: " + selectedStock.code);
                        } else {
                            SetStatusText("跳转失败，请确保通达信正在运行");
                            LOG_WARNING("Failed to jump to K-line for stock: " + selectedStock.code);
                        }
                    }
                }
            }
            break;

        case NM_CUSTOMDRAW:
            {
                // 自定义绘制，用于设置颜色
                LPNMLVCUSTOMDRAW pcd = (LPNMLVCUSTOMDRAW)lParam;
                return ListViewCustomDraw::HandleCustomDraw(lParam, m_stocks);
            }
            break;
        }
    }
}

std::wstring UIManager::FormatNumber(double value, int precision) {
    std::wostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::wstring UIManager::FormatVolume(int64_t volume) {
    if (volume >= 100000000) {  // 亿
        return FormatNumber(volume / 100000000.0, 2) + L"亿";
    } else if (volume >= 10000) {  // 万
        return FormatNumber(volume / 10000.0, 2) + L"万";
    } else {
        return std::to_wstring(volume);
    }
}

// ListViewCustomDraw implementation
LRESULT ListViewCustomDraw::HandleCustomDraw(LPARAM lParam, const std::vector<StockInfo>& stocks) {
    LPNMLVCUSTOMDRAW pcd = (LPNMLVCUSTOMDRAW)lParam;

    switch (pcd->nmcd.dwDrawStage) {
    case CDDS_PREPAINT:
        return CDRF_NOTIFYITEMDRAW;

    case CDDS_ITEMPREPAINT:
        {
            int itemIndex = static_cast<int>(pcd->nmcd.dwItemSpec);
            if (itemIndex >= 0 && itemIndex < static_cast<int>(stocks.size())) {
                const StockInfo& stock = stocks[itemIndex];

                // 设置文字颜色
                pcd->clrText = GetTextColor(stock.change_rate);

                // 设置背景颜色
                pcd->clrTextBk = GetBackgroundColor(itemIndex);
            }
            return CDRF_NEWFONT;
        }

    default:
        return CDRF_DODEFAULT;
    }
}

COLORREF ListViewCustomDraw::GetTextColor(double changeRate) {
    if (changeRate > 0) {
        return UIManager::COLOR_RED;    // 上涨红色
    } else if (changeRate < 0) {
        return UIManager::COLOR_GREEN;  // 下跌绿色
    } else {
        return UIManager::COLOR_BLACK;  // 平盘黑色
    }
}

COLORREF ListViewCustomDraw::GetBackgroundColor(int index) {
    // 交替行背景色
    if (index % 2 == 0) {
        return RGB(255, 255, 255);  // 白色
    } else {
        return RGB(248, 248, 248);  // 浅灰色
    }
}
