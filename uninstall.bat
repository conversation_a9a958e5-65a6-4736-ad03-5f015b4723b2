@echo off
setlocal enabledelayedexpansion

echo 通达信开盘啦联动插件卸载脚本
echo ================================

REM 查找通达信安装目录
set TDX_PATH=
set TDX_DLL_PATH=

REM 常见安装路径
set PATHS[0]=C:\tdx
set PATHS[1]=C:\Program Files\tdx
set PATHS[2]=C:\Program Files (x86)\tdx
set PATHS[3]=D:\tdx
set PATHS[4]=E:\tdx

echo 正在查找通达信安装目录...

for /L %%i in (0,1,4) do (
    if exist "!PATHS[%%i]!\TdxW.exe" (
        set TDX_PATH=!PATHS[%%i]!
        set TDX_DLL_PATH=!PATHS[%%i]!\T0002\dlls
        echo 找到通达信安装目录: !TDX_PATH!
        goto :found
    )
)

REM 如果没找到，让用户手动输入
echo 未找到通达信安装目录，请手动输入:
set /p TDX_PATH=请输入通达信安装目录 (例如: C:\tdx): 

if not exist "%TDX_PATH%\TdxW.exe" (
    echo 错误: 指定目录中找不到 TdxW.exe
    pause
    exit /b 1
)

set TDX_DLL_PATH=%TDX_PATH%\T0002\dlls

:found

echo.
echo 插件目录: %TDX_DLL_PATH%
echo.

REM 检查插件文件是否存在
if not exist "%TDX_DLL_PATH%\TdxKplPlugin.dll" (
    echo 未找到插件文件，可能已经卸载
    pause
    exit /b 0
)

REM 确认卸载
echo 即将删除以下文件:
echo - %TDX_DLL_PATH%\TdxKplPlugin.dll
if exist "%TDX_DLL_PATH%\config.ini" (
    echo - %TDX_DLL_PATH%\config.ini
)
if exist "%TDX_DLL_PATH%\TdxKplPlugin.log" (
    echo - %TDX_DLL_PATH%\TdxKplPlugin.log
)
echo.

set /p CONFIRM=确认卸载? (Y/N): 
if /i not "%CONFIRM%"=="Y" (
    echo 取消卸载
    pause
    exit /b 0
)

echo.
echo 正在卸载...

REM 删除DLL文件
if exist "%TDX_DLL_PATH%\TdxKplPlugin.dll" (
    echo 删除 TdxKplPlugin.dll...
    del "%TDX_DLL_PATH%\TdxKplPlugin.dll"
    if !ERRORLEVEL! neq 0 (
        echo 警告: 删除DLL文件失败，可能正在被使用
        echo 请关闭通达信后重试
    )
)

REM 删除配置文件
if exist "%TDX_DLL_PATH%\config.ini" (
    echo 删除 config.ini...
    del "%TDX_DLL_PATH%\config.ini"
)

REM 删除日志文件
if exist "%TDX_DLL_PATH%\TdxKplPlugin.log" (
    echo 删除 TdxKplPlugin.log...
    del "%TDX_DLL_PATH%\TdxKplPlugin.log"
)

if exist "%TDX_DLL_PATH%\TdxKplPlugin.log.bak" (
    echo 删除 TdxKplPlugin.log.bak...
    del "%TDX_DLL_PATH%\TdxKplPlugin.log.bak"
)

echo.
echo ================================
echo 卸载完成！
echo.
echo 插件已从通达信中移除
echo 重启通达信后菜单中将不再显示"开盘啦热榜"选项
echo.

pause
