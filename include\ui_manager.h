#pragma once

#include "common.h"
#include <commctrl.h>

// UI管理器类
class UIManager {
public:
    UIManager();
    ~UIManager();
    
    // 初始化
    bool Initialize(HWND parentWnd);
    
    // 清理
    void Cleanup();
    
    // 创建控件
    bool CreateControls(HWND parentWnd, int width, int height);
    
    // 更新股票列表
    void UpdateStockList(const std::vector<StockInfo>& stocks);
    
    // 清空股票列表
    void ClearStockList();
    
    // 设置状态文本
    void SetStatusText(const std::string& text);
    
    // 获取选中的股票
    StockInfo GetSelectedStock();
    
    // 获取当前选择的榜单类型
    HotListType GetSelectedListType();
    
    // 设置榜单类型
    void SetListType(HotListType type);
    
    // 调整控件大小
    void ResizeControls(int width, int height);
    
    // 处理控件消息
    void OnCommand(WPARAM wParam, LPARAM lParam);
    void OnNotify(WPARAM wParam, LPARAM lParam);
    
    // 获取控件句柄
    HWND GetListView() const { return m_hListView; }
    HWND GetComboBox() const { return m_hComboBox; }
    HWND GetRefreshButton() const { return m_hRefreshBtn; }
    HWND GetStatusText() const { return m_hStatusText; }
    
private:
    // 初始化ListView
    void InitializeListView();
    
    // 初始化ComboBox
    void InitializeComboBox();
    
    // 添加ListView列
    void AddListViewColumn(int index, const std::wstring& text, int width);
    
    // 添加ListView项
    void AddListViewItem(int index, const StockInfo& stock);
    
    // 格式化数字显示
    std::wstring FormatNumber(double value, int precision = 2);
    std::wstring FormatVolume(int64_t volume);
    
    // 设置ListView项颜色
    void SetItemColor(int index, COLORREF color);
    
private:
    HWND m_hParentWnd = nullptr;
    HWND m_hListView = nullptr;
    HWND m_hComboBox = nullptr;
    HWND m_hRefreshBtn = nullptr;
    HWND m_hStatusText = nullptr;
    
    std::vector<StockInfo> m_stocks;
    HotListType m_currentListType = HotListType::LIMIT_UP;
    
    // 字体
    HFONT m_hFont = nullptr;
    HFONT m_hBoldFont = nullptr;
    
    // 颜色
    static const COLORREF COLOR_RED;    // 上涨红色
    static const COLORREF COLOR_GREEN;  // 下跌绿色
    static const COLORREF COLOR_BLACK;  // 平盘黑色
    
    // 列宽度
    static const int COL_WIDTH_RANK;
    static const int COL_WIDTH_CODE;
    static const int COL_WIDTH_NAME;
    static const int COL_WIDTH_PRICE;
    static const int COL_WIDTH_CHANGE;
    static const int COL_WIDTH_VOLUME;
    static const int COL_WIDTH_REASON;
};

// ListView自定义绘制类
class ListViewCustomDraw {
public:
    static LRESULT HandleCustomDraw(LPARAM lParam, const std::vector<StockInfo>& stocks);
    
private:
    static COLORREF GetTextColor(double changeRate);
    static COLORREF GetBackgroundColor(int index);
};
