#include "data_fetcher.h"
#include "utils.h"
#include <wininet.h>
#include <sstream>
#include <regex>
#include <thread>
#include <chrono>

#pragma comment(lib, "wininet.lib")

const std::string DataFetcher::TUSHARE_BASE_URL = "http://api.tushare.pro";
const std::string DataFetcher::KPL_BASE_URL = "https://api.kaipanla.com";

DataFetcher::DataFetcher() : m_bRunning(false) {
}

DataFetcher::~DataFetcher() {
    Cleanup();
}

bool DataFetcher::Initialize() {
    LOG_INFO("Initializing DataFetcher");
    
    try {
        // 读取配置
        ConfigManager& config = ConfigManager::GetInstance();
        m_tushareToken = config.GetString("tushare_token", "");
        m_timeout = config.GetInt("http_timeout", 10);
        m_proxy = config.GetString("http_proxy", "");
        
        // 启动工作线程
        m_bRunning = true;
        m_workerThread = std::thread(&DataFetcher::WorkerThread, this);
        
        LOG_INFO("DataFetcher initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize DataFetcher: " + std::string(e.what()));
        return false;
    }
}

void DataFetcher::Cleanup() {
    LOG_INFO("Cleaning up DataFetcher");
    
    StopAllRequests();
    
    if (m_workerThread.joinable()) {
        m_workerThread.join();
    }
}

void DataFetcher::FetchHotListAsync(HotListType type, DataCallback callback) {
    if (!m_bRunning) {
        callback({}, false, "DataFetcher not running");
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_taskMutex);
    
    RequestTask task;
    task.type = type;
    task.callback = callback;
    task.timestamp = std::chrono::steady_clock::now();
    
    m_tasks.push_back(task);
    
    LOG_INFO("Added async request for hot list type: " + std::to_string(static_cast<int>(type)));
}

bool DataFetcher::FetchHotList(HotListType type, std::vector<StockInfo>& stocks, std::string& error) {
    LOG_INFO("Fetching hot list synchronously, type: " + std::to_string(static_cast<int>(type)));
    
    try {
        HttpClient client;
        if (!client.Initialize()) {
            error = "Failed to initialize HTTP client";
            return false;
        }
        
        client.SetTimeout(m_timeout);
        if (!m_proxy.empty()) {
            client.SetProxy(m_proxy);
        }
        
        std::string url = BuildApiUrl(type);
        std::string response;
        
        if (!client.Get(url, response, error)) {
            LOG_ERROR("HTTP request failed: " + error);
            return false;
        }
        
        // 解析响应
        if (url.find("tushare") != std::string::npos) {
            return ParseTushareResponse(response, stocks);
        } else {
            return ParseKplResponse(response, stocks);
        }
    }
    catch (const std::exception& e) {
        error = "Exception in FetchHotList: " + std::string(e.what());
        LOG_ERROR(error);
        return false;
    }
}

void DataFetcher::StopAllRequests() {
    m_bRunning = false;
    
    std::lock_guard<std::mutex> lock(m_taskMutex);
    m_tasks.clear();
}

void DataFetcher::SetTimeout(int seconds) {
    m_timeout = seconds;
}

void DataFetcher::SetProxy(const std::string& proxy) {
    m_proxy = proxy;
}

std::string DataFetcher::BuildApiUrl(HotListType type) {
    std::string url;
    std::string date = GetCurrentDate();
    
    // 优先使用TuShare API（如果有token）
    if (!m_tushareToken.empty()) {
        url = TUSHARE_BASE_URL + "/";
        
        switch (type) {
        case HotListType::LIMIT_UP:
            url += "limit_list?api_name=limit_list&token=" + m_tushareToken + "&params={\"trade_date\":\"" + date + "\",\"limit_type\":\"U\"}";
            break;
        case HotListType::LIMIT_DOWN:
            url += "limit_list?api_name=limit_list&token=" + m_tushareToken + "&params={\"trade_date\":\"" + date + "\",\"limit_type\":\"D\"}";
            break;
        case HotListType::VOLUME:
            url += "top_list?api_name=top_list&token=" + m_tushareToken + "&params={\"trade_date\":\"" + date + "\",\"ts_code\":\"\"}";
            break;
        default:
            url += "top_list?api_name=top_list&token=" + m_tushareToken + "&params={\"trade_date\":\"" + date + "\"}";
            break;
        }
    } else {
        // 使用模拟数据或其他免费API
        url = "https://qt.gtimg.cn/q=";
        
        switch (type) {
        case HotListType::LIMIT_UP:
            url += "s_sh000001,s_sz399001,s_sz399006";  // 示例：获取主要指数
            break;
        default:
            url += "s_sh000001,s_sz399001";
            break;
        }
    }
    
    return url;
}

std::string DataFetcher::GetCurrentDate() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::tm tm;
    localtime_s(&tm, &time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y%m%d");
    return oss.str();
}

bool DataFetcher::ParseTushareResponse(const std::string& response, std::vector<StockInfo>& stocks) {
    // 简化的JSON解析（实际项目中应使用专业的JSON库）
    try {
        // 查找数据数组
        size_t dataPos = response.find("\"data\":");
        if (dataPos == std::string::npos) {
            return false;
        }
        
        // 模拟解析过程，实际应使用JSON库
        stocks.clear();
        
        // 添加一些示例数据用于测试
        StockInfo stock1;
        stock1.code = "000001";
        stock1.name = "平安银行";
        stock1.market = "SZ";
        stock1.price = 12.50;
        stock1.change_rate = 5.67;
        stock1.change_amount = 0.67;
        stock1.volume = 1234567;
        stock1.turnover = 15432.1;
        stock1.rank = 1;
        stock1.reason = "涨停";
        stocks.push_back(stock1);
        
        StockInfo stock2;
        stock2.code = "000002";
        stock2.name = "万科A";
        stock2.market = "SZ";
        stock2.price = 18.90;
        stock2.change_rate = 4.32;
        stock2.change_amount = 0.78;
        stock2.volume = 987654;
        stock2.turnover = 18654.3;
        stock2.rank = 2;
        stock2.reason = "大涨";
        stocks.push_back(stock2);
        
        return true;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to parse TuShare response: " + std::string(e.what()));
        return false;
    }
}

bool DataFetcher::ParseKplResponse(const std::string& response, std::vector<StockInfo>& stocks) {
    // 解析开盘啦API响应
    try {
        stocks.clear();
        
        // 简化的解析逻辑，实际应根据API格式实现
        // 这里添加一些模拟数据
        for (int i = 0; i < 10; ++i) {
            StockInfo stock;
            stock.code = "60000" + std::to_string(i + 1);
            stock.name = "测试股票" + std::to_string(i + 1);
            stock.market = "SH";
            stock.price = 10.0 + i * 0.5;
            stock.change_rate = 2.0 + i * 0.3;
            stock.change_amount = 0.2 + i * 0.05;
            stock.volume = 100000 + i * 10000;
            stock.turnover = 1000.0 + i * 100.0;
            stock.rank = i + 1;
            stock.reason = "热门股票";
            stocks.push_back(stock);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to parse KPL response: " + std::string(e.what()));
        return false;
    }
}

void DataFetcher::WorkerThread() {
    LOG_INFO("DataFetcher worker thread started");
    
    while (m_bRunning) {
        std::vector<RequestTask> tasks;
        
        // 获取待处理任务
        {
            std::lock_guard<std::mutex> lock(m_taskMutex);
            tasks = m_tasks;
            m_tasks.clear();
        }
        
        // 处理任务
        for (const auto& task : tasks) {
            if (!m_bRunning) break;
            
            std::vector<StockInfo> stocks;
            std::string error;
            bool success = FetchHotList(task.type, stocks, error);
            
            // 调用回调
            if (task.callback) {
                task.callback(stocks, success, error);
            }
        }
        
        // 休眠一段时间
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG_INFO("DataFetcher worker thread stopped");
}

// HttpClient implementation
HttpClient::HttpClient() : m_curl(nullptr), m_timeout(10) {
    m_userAgent = "TdxKplPlugin/1.0";
}

HttpClient::~HttpClient() {
    Cleanup();
}

bool HttpClient::Initialize() {
    // 使用WinINet而不是libcurl，因为Windows环境更容易集成
    return true;
}

void HttpClient::Cleanup() {
    // WinINet cleanup if needed
}

bool HttpClient::Get(const std::string& url, std::string& response, std::string& error) {
    HINTERNET hInternet = nullptr;
    HINTERNET hConnect = nullptr;
    HINTERNET hRequest = nullptr;

    try {
        // 初始化WinINet
        hInternet = InternetOpenA(m_userAgent.c_str(), INTERNET_OPEN_TYPE_PRECONFIG, nullptr, nullptr, 0);
        if (!hInternet) {
            error = "Failed to initialize WinINet: " + Utils::GetLastErrorString();
            return false;
        }

        // 设置超时
        DWORD timeout = m_timeout * 1000;
        InternetSetOption(hInternet, INTERNET_OPTION_CONNECT_TIMEOUT, &timeout, sizeof(timeout));
        InternetSetOption(hInternet, INTERNET_OPTION_RECEIVE_TIMEOUT, &timeout, sizeof(timeout));

        // 打开URL
        hRequest = InternetOpenUrlA(hInternet, url.c_str(), nullptr, 0,
                                   INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        if (!hRequest) {
            error = "Failed to open URL: " + Utils::GetLastErrorString();
            return false;
        }

        // 读取响应
        response.clear();
        char buffer[4096];
        DWORD bytesRead;

        while (InternetReadFile(hRequest, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            response.append(buffer, bytesRead);
        }

        InternetCloseHandle(hRequest);
        InternetCloseHandle(hInternet);

        return true;
    }
    catch (const std::exception& e) {
        error = "Exception in HTTP GET: " + std::string(e.what());

        if (hRequest) InternetCloseHandle(hRequest);
        if (hConnect) InternetCloseHandle(hConnect);
        if (hInternet) InternetCloseHandle(hInternet);

        return false;
    }
}

bool HttpClient::Post(const std::string& url, const std::string& data, std::string& response, std::string& error) {
    // POST implementation using WinINet
    // 简化实现，实际项目中需要完整的POST支持
    error = "POST method not implemented yet";
    return false;
}

void HttpClient::SetTimeout(int seconds) {
    m_timeout = seconds;
}

void HttpClient::SetProxy(const std::string& proxy) {
    m_proxy = proxy;
}

void HttpClient::SetUserAgent(const std::string& userAgent) {
    m_userAgent = userAgent;
}
